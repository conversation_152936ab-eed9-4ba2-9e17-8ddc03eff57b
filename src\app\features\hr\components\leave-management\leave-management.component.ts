import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject, takeUntil } from 'rxjs';

import { 
  LeaveService, 
  LeaveRequest, 
  LeaveBalance, 
  LeaveType, 
  LeaveStatus,
  ApproveRejectLeaveDto 
} from '../../../../core/services/leave.service';
import { PaginationParams } from '../../../../core/models';

interface LeaveBalanceDisplay {
  employeeId: string;
  employeeName: string;
  annualLeave: { used: number; total: number };
  sickLeave: { used: number; total: number };
  personalLeave: { used: number; total: number };
  maternityLeave: { used: number; total: number };
}

@Component({
  selector: 'app-leave-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatChipsModule,
    MatTabsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDialogModule,
    MatTooltipModule,
    MatMenuModule,
    MatCheckboxModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="leave-management">
      <div class="header">
        <h1>
          <mat-icon>event_available</mat-icon>
          Leave Management
        </h1>
        <p>Manage employee leave requests and balances</p>
      </div>

      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading leave data...</p>
      </div>

      <mat-tab-group class="main-tabs" *ngIf="!loading">
        <!-- Pending Requests Tab -->
        <mat-tab label="Pending Requests ({{pendingRequests.length}})">
          <div class="tab-content">
            <mat-card class="requests-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>pending_actions</mat-icon>
                  Requests Awaiting Approval
                </mat-card-title>
                <div class="header-actions">
                  <button mat-raised-button color="primary" (click)="bulkApprove()" 
                          [disabled]="selectedRequests.length === 0">
                    <mat-icon>done_all</mat-icon>
                    Bulk Approve ({{selectedRequests.length}})
                  </button>
                </div>
              </mat-card-header>

              <mat-card-content>
                <div class="table-container">
                  <table mat-table [dataSource]="pendingRequests" class="requests-table">
                    <!-- Select Column -->
                    <ng-container matColumnDef="select">
                      <th mat-header-cell *matHeaderCellDef>
                        <mat-checkbox (change)="toggleAllSelection($event)" 
                                      [checked]="isAllSelected()"
                                      [indeterminate]="isPartiallySelected()">
                        </mat-checkbox>
                      </th>
                      <td mat-cell *matCellDef="let request">
                        <mat-checkbox (change)="toggleSelection(request, $event)"
                                      [checked]="isSelected(request)">
                        </mat-checkbox>
                      </td>
                    </ng-container>

                    <!-- Employee Column -->
                    <ng-container matColumnDef="employee">
                      <th mat-header-cell *matHeaderCellDef>Employee</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="employee-info">
                          <strong>{{request.employeeName}}</strong>
                          <small>ID: {{request.employeeId}}</small>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Leave Type Column -->
                    <ng-container matColumnDef="leaveType">
                      <th mat-header-cell *matHeaderCellDef>Leave Type</th>
                      <td mat-cell *matCellDef="let request">
                        <mat-chip [class]="getLeaveTypeClass(request.leaveType)">
                          {{getLeaveTypeLabel(request.leaveType)}}
                        </mat-chip>
                      </td>
                    </ng-container>

                    <!-- Duration Column -->
                    <ng-container matColumnDef="duration">
                      <th mat-header-cell *matHeaderCellDef>Duration</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="duration-info">
                          <strong>{{request.days}} days</strong>
                          <small>{{request.startDate | date:'MMM dd'}} - {{request.endDate | date:'MMM dd, yyyy'}}</small>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Reason Column -->
                    <ng-container matColumnDef="reason">
                      <th mat-header-cell *matHeaderCellDef>Reason</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="reason-text" [matTooltip]="request.reason">
                          {{request.reason.length > 50 ? (request.reason | slice:0:50) + '...' : request.reason}}
                        </div>
                      </td>
                    </ng-container>

                    <!-- Submitted Column -->
                    <ng-container matColumnDef="submitted">
                      <th mat-header-cell *matHeaderCellDef>Submitted</th>
                      <td mat-cell *matCellDef="let request">
                        {{request.submittedDate | date:'MMM dd, yyyy'}}
                      </td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef>Actions</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="action-buttons">
                          <button mat-icon-button color="primary" (click)="approveRequest(request)" 
                                  matTooltip="Approve">
                            <mat-icon>check_circle</mat-icon>
                          </button>
                          <button mat-icon-button color="warn" (click)="rejectRequest(request)" 
                                  matTooltip="Reject">
                            <mat-icon>cancel</mat-icon>
                          </button>
                          <button mat-icon-button (click)="viewDetails(request)" matTooltip="View Details">
                            <mat-icon>visibility</mat-icon>
                          </button>
                        </div>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="pendingColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: pendingColumns;" class="request-row"></tr>
                  </table>
                </div>

                <div *ngIf="pendingRequests.length === 0" class="no-data">
                  <mat-icon>assignment_turned_in</mat-icon>
                  <h3>No Pending Requests</h3>
                  <p>All leave requests have been processed.</p>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- All Requests Tab -->
        <mat-tab label="All Requests">
          <div class="tab-content">
            <mat-card class="filters-card">
              <mat-card-content>
                <div class="filters-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Search Employee</mat-label>
                    <input matInput [(ngModel)]="searchTerm" (input)="onFilterChange()" placeholder="Employee name or ID">
                    <mat-icon matSuffix>search</mat-icon>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Leave Type</mat-label>
                    <mat-select [(ngModel)]="selectedLeaveType" (selectionChange)="onFilterChange()">
                      <mat-option value="">All Types</mat-option>
                      <mat-option [value]="LeaveType.AnnualLeave">Annual Leave</mat-option>
                      <mat-option [value]="LeaveType.SickLeave">Sick Leave</mat-option>
                      <mat-option [value]="LeaveType.PersonalLeave">Personal Leave</mat-option>
                      <mat-option [value]="LeaveType.MaternityLeave">Maternity Leave</mat-option>
                      <mat-option [value]="LeaveType.PaternityLeave">Paternity Leave</mat-option>
                      <mat-option [value]="LeaveType.EmergencyLeave">Emergency Leave</mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Status</mat-label>
                    <mat-select [(ngModel)]="selectedStatus" (selectionChange)="onFilterChange()">
                      <mat-option value="">All Status</mat-option>
                      <mat-option [value]="LeaveStatus.Pending">Pending</mat-option>
                      <mat-option [value]="LeaveStatus.Approved">Approved</mat-option>
                      <mat-option [value]="LeaveStatus.Rejected">Rejected</mat-option>
                      <mat-option [value]="LeaveStatus.Cancelled">Cancelled</mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Date From</mat-label>
                    <input matInput [matDatepicker]="startPicker" [(ngModel)]="filterStartDate" (dateChange)="onFilterChange()">
                    <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                    <mat-datepicker #startPicker></mat-datepicker>
                  </mat-form-field>

                  <button mat-raised-button color="accent" (click)="clearFilters()" *ngIf="hasActiveFilters()">
                    <mat-icon>clear</mat-icon>
                    Clear Filters
                  </button>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="all-requests-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>list</mat-icon>
                  All Leave Requests ({{totalCount}})
                </mat-card-title>
              </mat-card-header>

              <mat-card-content>
                <div class="table-container">
                  <table mat-table [dataSource]="allRequests" class="requests-table">
                    <!-- Employee Column -->
                    <ng-container matColumnDef="employee">
                      <th mat-header-cell *matHeaderCellDef>Employee</th>
                      <td mat-cell *matCellDef="let request">{{request.employeeName}}</td>
                    </ng-container>

                    <!-- Leave Type Column -->
                    <ng-container matColumnDef="leaveType">
                      <th mat-header-cell *matHeaderCellDef>Type</th>
                      <td mat-cell *matCellDef="let request">
                        <mat-chip [class]="getLeaveTypeClass(request.leaveType)">
                          {{getLeaveTypeLabel(request.leaveType)}}
                        </mat-chip>
                      </td>
                    </ng-container>

                    <!-- Duration Column -->
                    <ng-container matColumnDef="duration">
                      <th mat-header-cell *matHeaderCellDef>Duration</th>
                      <td mat-cell *matCellDef="let request">{{request.days}} days</td>
                    </ng-container>

                    <!-- Status Column -->
                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let request">
                        <mat-chip [class]="getStatusClass(request.status)">
                          {{getLeaveStatusLabel(request.status)}}
                        </mat-chip>
                      </td>
                    </ng-container>

                    <!-- Submitted Column -->
                    <ng-container matColumnDef="submitted">
                      <th mat-header-cell *matHeaderCellDef>Submitted</th>
                      <td mat-cell *matCellDef="let request">{{request.submittedDate | date:'MMM dd, yyyy'}}</td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef>Actions</th>
                      <td mat-cell *matCellDef="let request">
                        <button mat-icon-button (click)="viewDetails(request)" matTooltip="View Details">
                          <mat-icon>visibility</mat-icon>
                        </button>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="allColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: allColumns;" class="request-row"></tr>
                  </table>
                </div>

                <div *ngIf="allRequests.length === 0" class="no-data">
                  <mat-icon>search_off</mat-icon>
                  <h3>No requests found</h3>
                  <p>Try adjusting your filters or check back later.</p>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Leave Balances Tab -->
        <mat-tab label="Leave Balances">
          <div class="tab-content">
            <mat-card class="balances-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>account_balance</mat-icon>
                  Employee Leave Balances
                </mat-card-title>
                <div class="header-actions">
                  <button mat-raised-button color="primary" (click)="exportBalances()">
                    <mat-icon>download</mat-icon>
                    Export Report
                  </button>
                </div>
              </mat-card-header>

              <mat-card-content>
                <div class="balances-grid">
                  <div *ngFor="let balance of leaveBalancesDisplay" class="balance-card">
                    <div class="balance-header">
                      <h4>{{balance.employeeName}}</h4>
                      <small>ID: {{balance.employeeId}}</small>
                    </div>
                    
                    <div class="balance-types">
                      <div class="balance-type">
                        <span class="type-label">Annual Leave</span>
                        <div class="balance-bar">
                          <div class="balance-used" 
                               [style.width.%]="(balance.annualLeave.used / balance.annualLeave.total) * 100">
                          </div>
                        </div>
                        <span class="balance-text">
                          {{balance.annualLeave.used}}/{{balance.annualLeave.total}} days
                        </span>
                      </div>

                      <div class="balance-type">
                        <span class="type-label">Sick Leave</span>
                        <div class="balance-bar">
                          <div class="balance-used" 
                               [style.width.%]="(balance.sickLeave.used / balance.sickLeave.total) * 100">
                          </div>
                        </div>
                        <span class="balance-text">
                          {{balance.sickLeave.used}}/{{balance.sickLeave.total}} days
                        </span>
                      </div>

                      <div class="balance-type">
                        <span class="type-label">Personal Leave</span>
                        <div class="balance-bar">
                          <div class="balance-used" 
                               [style.width.%]="(balance.personalLeave.used / balance.personalLeave.total) * 100">
                          </div>
                        </div>
                        <span class="balance-text">
                          {{balance.personalLeave.used}}/{{balance.personalLeave.total}} days
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div *ngIf="leaveBalancesDisplay.length === 0" class="no-data">
                  <mat-icon>account_balance_wallet</mat-icon>
                  <h3>No balance data available</h3>
                  <p>Leave balance information will appear here once available.</p>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styles: [`
    .leave-management {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      margin-bottom: 2rem;
      text-align: center;
    }

    .header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      margin: 0 0 0.5rem 0;
      color: #2e7d32;
      font-size: 2rem;
    }

    .header p {
      color: #666;
      font-size: 1.1rem;
      margin: 0;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      gap: 1rem;
    }

    .main-tabs {
      margin-bottom: 2rem;
    }

    .tab-content {
      padding: 1rem 0;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    mat-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .filters-card {
      margin-bottom: 1rem;
    }

    .filters-row {
      display: flex;
      gap: 1rem;
      align-items: flex-end;
      flex-wrap: wrap;
    }

    .filters-row mat-form-field {
      min-width: 200px;
    }

    .table-container {
      overflow-x: auto;
    }

    .requests-table {
      width: 100%;
    }

    .request-row:hover {
      background-color: #f5f5f5;
    }

    .employee-info {
      display: flex;
      flex-direction: column;
    }

    .employee-info strong {
      font-size: 0.95rem;
    }

    .employee-info small {
      color: #666;
      font-size: 0.8rem;
    }

    .duration-info {
      display: flex;
      flex-direction: column;
    }

    .duration-info strong {
      font-size: 0.95rem;
    }

    .duration-info small {
      color: #666;
      font-size: 0.8rem;
    }

    .reason-text {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
    }

    .leave-annual {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .leave-sick {
      background-color: #ffebee;
      color: #c62828;
    }

    .leave-personal {
      background-color: #f3e5f5;
      color: #7b1fa2;
    }

    .leave-maternity {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .leave-paternity {
      background-color: #e1f5fe;
      color: #0277bd;
    }

    .leave-emergency {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .status-pending {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .status-approved {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-rejected {
      background-color: #ffebee;
      color: #c62828;
    }

    .status-cancelled {
      background-color: #f5f5f5;
      color: #666;
    }

    .no-data {
      text-align: center;
      padding: 3rem;
      color: #666;
    }

    .no-data mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      color: #ccc;
    }

    .balances-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 1rem;
    }

    .balance-card {
      padding: 1.5rem;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }

    .balance-header {
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #e0e0e0;
    }

    .balance-header h4 {
      margin: 0 0 0.25rem 0;
      color: #333;
    }

    .balance-header small {
      color: #666;
    }

    .balance-types {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .balance-type {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .type-label {
      font-size: 0.9rem;
      font-weight: 500;
      color: #333;
    }

    .balance-bar {
      height: 8px;
      background-color: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
    }

    .balance-used {
      height: 100%;
      background-color: #4caf50;
      transition: width 0.3s ease;
    }

    .balance-text {
      font-size: 0.8rem;
      color: #666;
    }

    @media (max-width: 768px) {
      .filters-row {
        flex-direction: column;
        align-items: stretch;
      }

      .filters-row mat-form-field {
        min-width: 100%;
      }

      .action-buttons {
        flex-direction: column;
      }

      .balances-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class LeaveManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  pendingRequests: LeaveRequest[] = [];
  allRequests: LeaveRequest[] = [];
  leaveBalances: LeaveBalance[] = [];
  leaveBalancesDisplay: LeaveBalanceDisplay[] = [];
  selectedRequests: LeaveRequest[] = [];
  loading = false;
  totalCount = 0;

  pendingColumns: string[] = ['select', 'employee', 'leaveType', 'duration', 'reason', 'submitted', 'actions'];
  allColumns: string[] = ['employee', 'leaveType', 'duration', 'status', 'submitted', 'actions'];

  // Filters
  searchTerm = '';
  selectedLeaveType: LeaveType | '' = '';
  selectedStatus: LeaveStatus | '' = '';
  filterStartDate: Date | null = null;

  // Enums for template
  LeaveType = LeaveType;
  LeaveStatus = LeaveStatus;

  constructor(
    private leaveService: LeaveService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadLeaveRequests();
    this.loadLeaveBalances();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadLeaveRequests(): void {
    this.loading = true;
    
    const params: PaginationParams = {
      pageNumber: 1,
      pageSize: 50,
      searchTerm: this.searchTerm || undefined,
      type: this.selectedLeaveType || undefined,
      status: this.selectedStatus || undefined,
      sortBy: 'submittedDate',
      sortDirection: 'desc'
    };

    this.leaveService.getLeaveRequests(params).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response) => {
        console.log('Leave requests response:', response);
        this.allRequests = response.data || [];
        this.totalCount = response.totalCount;
        this.pendingRequests = this.allRequests.filter(r => r.status === LeaveStatus.Pending);
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading leave requests:', error);
        this.snackBar.open('Error loading leave requests. Please try again.', 'Close', { duration: 3000 });
        this.allRequests = [];
        this.pendingRequests = [];
        this.totalCount = 0;
        this.loading = false;
      }
    });
  }

  loadLeaveBalances(): void {
    this.leaveService.getLeaveBalances().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response) => {
        console.log('Leave balances response:', response);
        this.leaveBalances = response || [];
        this.processLeaveBalances();
      },
      error: (error) => {
        console.error('Error loading leave balances:', error);
        this.snackBar.open('Error loading leave balances.', 'Close', { duration: 3000 });
        this.leaveBalances = [];
        this.leaveBalancesDisplay = [];
      }
    });
  }

  processLeaveBalances(): void {
    // Group balances by employee
    const employeeBalances = new Map<string, LeaveBalanceDisplay>();

    this.leaveBalances.forEach(balance => {
      if (!employeeBalances.has(balance.employeeId)) {
        employeeBalances.set(balance.employeeId, {
          employeeId: balance.employeeId,
          employeeName: balance.employeeName,
          annualLeave: { used: 0, total: 0 },
          sickLeave: { used: 0, total: 0 },
          personalLeave: { used: 0, total: 0 },
          maternityLeave: { used: 0, total: 0 }
        });
      }

      const empBalance = employeeBalances.get(balance.employeeId)!;
      
      switch (balance.leaveType) {
        case LeaveType.AnnualLeave:
          empBalance.annualLeave = { used: balance.usedDays, total: balance.totalDays };
          break;
        case LeaveType.SickLeave:
          empBalance.sickLeave = { used: balance.usedDays, total: balance.totalDays };
          break;
        case LeaveType.PersonalLeave:
          empBalance.personalLeave = { used: balance.usedDays, total: balance.totalDays };
          break;
        case LeaveType.MaternityLeave:
          empBalance.maternityLeave = { used: balance.usedDays, total: balance.totalDays };
          break;
      }
    });

    this.leaveBalancesDisplay = Array.from(employeeBalances.values());
  }

  onFilterChange(): void {
    this.loadLeaveRequests();
  }

  hasActiveFilters(): boolean {
    return !!(this.searchTerm || this.selectedLeaveType || this.selectedStatus || this.filterStartDate);
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedLeaveType = '';
    this.selectedStatus = '';
    this.filterStartDate = null;
    this.loadLeaveRequests();
  }

  getLeaveTypeLabel(type: LeaveType): string {
    return this.leaveService.getLeaveTypeLabel(type);
  }

  getLeaveStatusLabel(status: LeaveStatus): string {
    return this.leaveService.getLeaveStatusLabel(status);
  }

  getLeaveTypeClass(leaveType: LeaveType): string {
    switch (leaveType) {
      case LeaveType.AnnualLeave: return 'leave-annual';
      case LeaveType.SickLeave: return 'leave-sick';
      case LeaveType.PersonalLeave: return 'leave-personal';
      case LeaveType.MaternityLeave: return 'leave-maternity';
      case LeaveType.PaternityLeave: return 'leave-paternity';
      case LeaveType.EmergencyLeave: return 'leave-emergency';
      default: return 'leave-annual';
    }
  }

  getStatusClass(status: LeaveStatus): string {
    switch (status) {
      case LeaveStatus.Pending: return 'status-pending';
      case LeaveStatus.Approved: return 'status-approved';
      case LeaveStatus.Rejected: return 'status-rejected';
      case LeaveStatus.Cancelled: return 'status-cancelled';
      default: return 'status-pending';
    }
  }

  toggleSelection(request: LeaveRequest, event: any): void {
    if (event.checked) {
      this.selectedRequests.push(request);
    } else {
      this.selectedRequests = this.selectedRequests.filter(r => r.id !== request.id);
    }
  }

  toggleAllSelection(event: any): void {
    if (event.checked) {
      this.selectedRequests = [...this.pendingRequests];
    } else {
      this.selectedRequests = [];
    }
  }

  isSelected(request: LeaveRequest): boolean {
    return this.selectedRequests.some(r => r.id === request.id);
  }

  isAllSelected(): boolean {
    return this.selectedRequests.length === this.pendingRequests.length && this.pendingRequests.length > 0;
  }

  isPartiallySelected(): boolean {
    return this.selectedRequests.length > 0 && this.selectedRequests.length < this.pendingRequests.length;
  }

  approveRequest(request: LeaveRequest): void {
    const approvalData: ApproveRejectLeaveDto = {
      comments: 'Approved by HR'
    };

    this.leaveService.approveLeaveRequest(request.id, approvalData).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        this.snackBar.open('Leave request approved successfully', 'Close', { duration: 3000 });
        this.loadLeaveRequests();
        this.loadLeaveBalances();
      },
      error: (error) => {
        console.error('Error approving request:', error);
        this.snackBar.open('Error approving request. Please try again.', 'Close', { duration: 3000 });
      }
    });
  }

  rejectRequest(request: LeaveRequest): void {
    const rejectionData: ApproveRejectLeaveDto = {
      comments: 'Rejected by HR'
    };

    this.leaveService.rejectLeaveRequest(request.id, rejectionData).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        this.snackBar.open('Leave request rejected', 'Close', { duration: 3000 });
        this.loadLeaveRequests();
      },
      error: (error) => {
        console.error('Error rejecting request:', error);
        this.snackBar.open('Error rejecting request. Please try again.', 'Close', { duration: 3000 });
      }
    });
  }

  bulkApprove(): void {
    const requestIds = this.selectedRequests.map(r => r.id);
    
    this.leaveService.bulkApproveLeaveRequests(requestIds, 'Bulk approved by HR').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        this.snackBar.open(`${requestIds.length} requests approved successfully`, 'Close', { duration: 3000 });
        this.selectedRequests = [];
        this.loadLeaveRequests();
        this.loadLeaveBalances();
      },
      error: (error) => {
        console.error('Error bulk approving requests:', error);
        this.snackBar.open('Error approving requests. Please try again.', 'Close', { duration: 3000 });
      }
    });
  }

  viewDetails(request: LeaveRequest): void {
    console.log('View details:', request);
    this.snackBar.open('View details functionality coming soon', 'Close', { duration: 2000 });
  }

  exportBalances(): void {
    this.leaveService.exportLeaveReport('excel').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response) => {
        // Handle file download
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `leave-balances-${new Date().toISOString().split('T')[0]}.xlsx`;
        a.click();
        window.URL.revokeObjectURL(url);
        this.snackBar.open('Leave balances exported successfully', 'Close', { duration: 3000 });
      },
      error: (error) => {
        console.error('Error exporting leave balances:', error);
        this.snackBar.open('Error exporting leave balances. Please try again.', 'Close', { duration: 3000 });
      }
    });
  }
}