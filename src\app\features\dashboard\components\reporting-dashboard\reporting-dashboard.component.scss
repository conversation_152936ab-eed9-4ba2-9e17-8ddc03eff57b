.reporting-dashboard-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 2rem;

  h1 {
    color: #1976d2;
    margin-bottom: 0.5rem;
  }

  p {
    color: #666;
    font-size: 1.1rem;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;

  p {
    margin-top: 1rem;
    color: #666;
  }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  .mat-card-header {
    padding-bottom: 1rem;
  }

  .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1976d2;
    line-height: 1;
  }

  .stat-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.5rem;
  }

  .users-icon {
    background-color: #4caf50;
    color: white;
  }

  .workflows-icon {
    background-color: #ff9800;
    color: white;
  }

  .requests-icon {
    background-color: #2196f3;
    color: white;
  }

  .pending-icon {
    background-color: #f44336;
    color: white;
  }
}

.admin-actions {
  h2 {
    color: #333;
    margin-bottom: 1.5rem;
  }
}

.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .mat-card-header {
    padding-bottom: 1rem;
  }

  .mat-card-title {
    color: #333;
    font-weight: 600;
  }

  .mat-card-subtitle {
    color: #666;
    margin-top: 0.5rem;
  }

  .mat-card-actions {
    padding-top: 1rem;

    button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
}

.quick-reports {
  h2 {
    color: #333;
    margin-bottom: 1.5rem;
  }

  .report-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;

    button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      min-width: 150px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .reporting-dashboard-container {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-cards {
    grid-template-columns: 1fr;
  }

  .report-buttons {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    text-align: center;

    h1 {
      font-size: 1.8rem;
    }
  }

  .stat-card .stat-number {
    font-size: 2rem;
  }
}
