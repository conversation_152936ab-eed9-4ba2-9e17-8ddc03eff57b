import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { 
  UserService, 
  RequestService, 
  LeaveService, 
  PerformanceReviewService 
} from '../../../../core/services';
import { RequestSummary } from '../../../../core/models/request.models';
import { LeaveStatistics } from '../../../../core/services/leave.service';
import { PerformanceReviewStatistics } from '../../../../core/services/performance-review.service';

interface HRMetrics {
  totalEmployees: number;
  activeEmployees: number;
  pendingRequests: number;
  processedThisMonth: number;
  averageProcessingTime: number;
  leaveRequests: number;
  expenseReports: number;
  trainingRequests: number;
  performanceReviews: number;
}

interface QuickAction {
  title: string;
  description: string;
  icon: string;
  route: string;
  color: string;
}

@Component({
  selector: 'app-hr-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatTableModule,
    MatChipsModule,
    MatProgressBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="hr-dashboard">
      <div class="dashboard-header">
        <h1>
          <mat-icon>people</mat-icon>
          HR Dashboard
        </h1>
        <p>Manage employee requests and HR processes</p>
      </div>

      <!-- Loading Indicator -->
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Loading dashboard data...</p>
      </div>

      <!-- Key Metrics -->
      <div *ngIf="!isLoading">
      <div class="metrics-grid">
        <mat-card class="metric-card employees">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>group</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.totalEmployees}}</h3>
                <p>Total Employees</p>
                <small>{{hrMetrics.activeEmployees}} active</small>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card requests">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>assignment</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.pendingRequests}}</h3>
                <p>Pending Requests</p>
                <small>Awaiting HR review</small>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card processed">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>done_all</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.processedThisMonth}}</h3>
                <p>Processed This Month</p>
                <small>{{hrMetrics.averageProcessingTime}}h avg time</small>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card performance">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>star_rate</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.performanceReviews}}</h3>
                <p>Performance Reviews</p>
                <small>Due this quarter</small>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Quick Actions -->
      <mat-card class="quick-actions-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>flash_on</mat-icon>
            Quick Actions
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="quick-actions-grid">
            <div *ngFor="let action of quickActions" 
                 class="quick-action-item" 
                 [routerLink]="action.route"
                 [class]="'action-' + action.color">
              <mat-icon>{{action.icon}}</mat-icon>
              <h4>{{action.title}}</h4>
              <p>{{action.description}}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Request Types Overview -->
      <div class="overview-section">
        <mat-card class="request-types-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>pie_chart</mat-icon>
              Request Types Overview
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="request-types-grid">
              <div class="type-item">
                <div class="type-icon leave">
                  <mat-icon>event_available</mat-icon>
                </div>
                <div class="type-info">
                  <h4>{{hrMetrics.leaveRequests}}</h4>
                  <p>Leave Requests</p>
                  <mat-progress-bar mode="determinate" [value]="getLeaveProgress()"></mat-progress-bar>
                </div>
              </div>

              <div class="type-item">
                <div class="type-icon expense">
                  <mat-icon>receipt</mat-icon>
                </div>
                <div class="type-info">
                  <h4>{{hrMetrics.expenseReports}}</h4>
                  <p>Expense Reports</p>
                  <mat-progress-bar mode="determinate" [value]="getExpenseProgress()"></mat-progress-bar>
                </div>
              </div>

              <div class="type-item">
                <div class="type-icon training">
                  <mat-icon>school</mat-icon>
                </div>
                <div class="type-info">
                  <h4>{{hrMetrics.trainingRequests}}</h4>
                  <p>Training Requests</p>
                  <mat-progress-bar mode="determinate" [value]="getTrainingProgress()"></mat-progress-bar>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Recent Activity -->
        <mat-card class="recent-activity-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>history</mat-icon>
              Recent Activity
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="activity-list">
              <div *ngFor="let activity of recentActivities" class="activity-item">
                <div class="activity-icon" [class]="'activity-' + activity.type">
                  <mat-icon>{{activity.icon}}</mat-icon>
                </div>
                <div class="activity-content">
                  <h5>{{activity.title}}</h5>
                  <p>{{activity.description}}</p>
                  <small>{{activity.time}}</small>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Performance Summary -->
      <mat-card class="performance-summary-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>analytics</mat-icon>
            HR Performance Summary
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="performance-metrics">
            <div class="performance-item">
              <span class="label">Request Processing Rate:</span>
              <span class="value">{{getProcessingRate()}}%</span>
              <mat-progress-bar mode="determinate" [value]="getProcessingRate()"></mat-progress-bar>
            </div>
            <div class="performance-item">
              <span class="label">Leave Approval Rate:</span>
              <span class="value">{{getLeaveApprovalRate()}}%</span>
              <mat-progress-bar mode="determinate" [value]="getLeaveApprovalRate()"></mat-progress-bar>
            </div>
            <div class="performance-item">
              <span class="label">Performance Review Completion:</span>
              <span class="value">{{getPerformanceCompletionRate()}}%</span>
              <mat-progress-bar mode="determinate" [value]="getPerformanceCompletionRate()"></mat-progress-bar>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .hr-dashboard {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .dashboard-header {
      margin-bottom: 2rem;
      text-align: center;
    }

    .dashboard-header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      margin: 0 0 0.5rem 0;
      color: #2e7d32;
      font-size: 2rem;
    }

    .dashboard-header p {
      color: #666;
      font-size: 1.1rem;
      margin: 0;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .metric-card {
      background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
      color: white;
    }

    .metric {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .metric-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .metric-icon mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .metric-info h3 {
      margin: 0;
      font-size: 2rem;
      font-weight: bold;
    }

    .metric-info p {
      margin: 0.25rem 0;
      font-size: 1rem;
    }

    .metric-info small {
      font-size: 0.8rem;
      opacity: 0.8;
    }

    .quick-actions-card {
      margin-bottom: 2rem;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .quick-action-item {
      padding: 1.5rem;
      border-radius: 8px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .quick-action-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .action-primary {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .action-success {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .action-warning {
      background-color: #fff3e0;
      color: #f57c00;
    }

    .action-info {
      background-color: #f3e5f5;
      color: #7b1fa2;
    }

    .quick-action-item mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      margin-bottom: 0.5rem;
    }

    .quick-action-item h4 {
      margin: 0.5rem 0;
      font-size: 1.1rem;
    }

    .quick-action-item p {
      margin: 0;
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .overview-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .request-types-grid {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .type-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 8px;
    }

    .type-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .type-icon.leave {
      background-color: #4caf50;
    }

    .type-icon.expense {
      background-color: #ff9800;
    }

    .type-icon.training {
      background-color: #2196f3;
    }

    .type-info {
      flex: 1;
    }

    .type-info h4 {
      margin: 0 0 0.25rem 0;
      font-size: 1.5rem;
    }

    .type-info p {
      margin: 0 0 0.5rem 0;
      color: #666;
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .activity-item {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 8px;
    }

    .activity-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;
    }

    .activity-approved {
      background-color: #4caf50;
    }

    .activity-pending {
      background-color: #ff9800;
    }

    .activity-rejected {
      background-color: #f44336;
    }

    .activity-content h5 {
      margin: 0 0 0.25rem 0;
      font-size: 1rem;
    }

    .activity-content p {
      margin: 0 0 0.25rem 0;
      color: #666;
      font-size: 0.9rem;
    }

    .activity-content small {
      color: #999;
      font-size: 0.8rem;
    }

    .performance-summary-card {
      margin-bottom: 2rem;
    }

    .performance-metrics {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .performance-item {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .performance-item .label {
      font-weight: 500;
      color: #333;
    }

    .performance-item .value {
      font-size: 1.2rem;
      font-weight: bold;
      color: #2e7d32;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      color: #666;
    }

    .loading-container p {
      margin-top: 1rem;
      font-size: 1.1rem;
    }

    @media (max-width: 768px) {
      .overview-section {
        grid-template-columns: 1fr;
      }

      .quick-actions-grid {
        grid-template-columns: 1fr;
      }

      .metrics-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class HRDashboardComponent implements OnInit {
  hrMetrics: HRMetrics = {
    totalEmployees: 0,
    activeEmployees: 0,
    pendingRequests: 0,
    processedThisMonth: 0,
    averageProcessingTime: 0,
    leaveRequests: 0,
    expenseReports: 0,
    trainingRequests: 0,
    performanceReviews: 0
  };

  isLoading = false;

  constructor(
    private userService: UserService,
    private requestService: RequestService,
    private leaveService: LeaveService,
    private performanceReviewService: PerformanceReviewService
  ) {}

  quickActions: QuickAction[] = [
    {
      title: 'Review Leave Requests',
      description: 'Process pending leave applications',
      icon: 'event_available',
      route: '/hr/leave-management',
      color: 'primary'
    },
    {
      title: 'Employee Records',
      description: 'Manage employee information',
      icon: 'badge',
      route: '/hr/employees',
      color: 'success'
    },
    {
      title: 'Performance Reviews',
      description: 'Conduct employee evaluations',
      icon: 'star_rate',
      route: '/hr/performance-reviews',
      color: 'warning'
    },
    {
      title: 'Generate Reports',
      description: 'Create HR analytics reports',
      icon: 'assessment',
      route: '/hr/reports',
      color: 'info'
    }
  ];

  recentActivities = [
    {
      title: 'Leave Request Approved',
      description: 'John Doe\'s vacation request has been approved',
      time: '2 hours ago',
      icon: 'check_circle',
      type: 'approved'
    },
    {
      title: 'New Employee Onboarding',
      description: 'Sarah Wilson started onboarding process',
      time: '4 hours ago',
      icon: 'person_add',
      type: 'pending'
    },
    {
      title: 'Performance Review Completed',
      description: 'Mike Johnson\'s quarterly review finished',
      time: '1 day ago',
      icon: 'star',
      type: 'approved'
    }
  ];

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.isLoading = true;
    console.log('Loading dashboard data...');

    // Start with just the request summary to test the connection
    this.requestService.getRequestSummary().subscribe({
      next: (requestSummary) => {
        console.log('Request summary loaded successfully:', requestSummary);
        
        // Update metrics with just the request summary data for now
        this.updateMetricsWithRequestSummary(requestSummary);
        
        // Try to load other services one by one
        this.loadAdditionalData();
      },
      error: (error) => {
        console.error('Error loading request summary:', error);
        this.isLoading = false;
      }
    });
  }

  private loadAdditionalData(): void {
    // Load other services with individual error handling
    const defaultUserStats = { totalUsers: 50, activeUsers: 45 }; // Fallback values
    const defaultLeaveStats = { pendingRequests: 0, approvedRequests: 0, rejectedRequests: 0, totalRequests: 0, totalDaysRequested: 0, totalDaysApproved: 0, mostRequestedLeaveType: '' };
    const defaultPerformanceStats = { pendingReviews: 0, completedReviews: 0, overdueReviews: 0, totalReviews: 0, averageRating: 0, completionRate: 0 };

    // Try user stats
    this.userService.getUserStats().subscribe({
      next: (userStats) => {
        console.log('User stats loaded:', userStats);
        this.hrMetrics.totalEmployees = userStats.totalUsers || defaultUserStats.totalUsers;
        this.hrMetrics.activeEmployees = userStats.activeUsers || defaultUserStats.activeUsers;
      },
      error: (error) => {
        console.error('User stats failed, using defaults:', error);
        this.hrMetrics.totalEmployees = defaultUserStats.totalUsers;
        this.hrMetrics.activeEmployees = defaultUserStats.activeUsers;
      }
    });

    // Try leave stats
    this.leaveService.getLeaveStatistics().subscribe({
      next: (leaveStats) => {
        console.log('Leave stats loaded:', leaveStats);
        this.leaveStats = leaveStats;
        this.hrMetrics.leaveRequests = leaveStats.pendingRequests || 0;
      },
      error: (error) => {
        console.error('Leave stats failed, using defaults:', error);
        this.leaveStats = defaultLeaveStats;
        this.hrMetrics.leaveRequests = 0;
      }
    });

    // Try performance stats
    this.performanceReviewService.getPerformanceReviewStatistics().subscribe({
      next: (performanceStats) => {
        console.log('Performance stats loaded:', performanceStats);
        this.performanceStats = performanceStats;
        this.hrMetrics.performanceReviews = performanceStats.pendingReviews || 0;
      },
      error: (error) => {
        console.error('Performance stats failed, using defaults:', error);
        this.performanceStats = defaultPerformanceStats;
        this.hrMetrics.performanceReviews = 0;
      }
    });

    this.isLoading = false;
  }

  private updateMetricsWithRequestSummary(requestSummary: RequestSummary): void {
    console.log('Updating metrics with request summary:', requestSummary);
    
    this.hrMetrics.pendingRequests = requestSummary.pendingRequests || 0;
    this.hrMetrics.processedThisMonth = requestSummary.approvedRequests || 0;
    this.hrMetrics.expenseReports = requestSummary.requestsByType?.expense || 0;
    this.hrMetrics.trainingRequests = requestSummary.requestsByType?.training || 0;

    // Update recent activities based on request summary
    this.recentActivities = [
      {
        title: 'Pending Requests',
        description: `${requestSummary.pendingRequests} requests awaiting approval`,
        time: 'Current',
        icon: 'assignment',
        type: 'pending'
      },
      {
        title: 'Leave Requests',
        description: `${requestSummary.requestsByType?.leave || 0} leave requests in system`,
        time: 'Current',
        icon: 'event_available',
        type: 'pending'
      },
      {
        title: 'Training Requests',
        description: `${requestSummary.requestsByType?.training || 0} training requests submitted`,
        time: 'Current',
        icon: 'school',
        type: 'pending'
      }
    ];
  }

  private updateMetrics(data: {
    userStats: any,
    requestSummary: RequestSummary,
    leaveStats: LeaveStatistics,
    performanceStats: PerformanceReviewStatistics
  }): void {
    // Store statistics for use in calculations
    this.leaveStats = data.leaveStats;
    this.performanceStats = data.performanceStats;

    // Update HR metrics with real data
    this.hrMetrics = {
      totalEmployees: data.userStats.totalUsers || 0,
      activeEmployees: data.userStats.activeUsers || 0,
      pendingRequests: data.requestSummary.pendingRequests || 0,
      processedThisMonth: data.requestSummary.approvedRequests || 0,
      averageProcessingTime: 2.5, // This might need to be calculated or provided by backend
      leaveRequests: data.leaveStats.pendingRequests || 0,
      expenseReports: data.requestSummary.requestsByType.expense || 0,
      trainingRequests: data.requestSummary.requestsByType.training || 0,
      performanceReviews: data.performanceStats.pendingReviews || 0
    };

    // Update recent activities with real data
    this.updateRecentActivities(data);
  }

  private updateRecentActivities(data: any): void {
    // This would ideally come from a recent activities API endpoint
    // For now, we'll generate some activities based on the data we have
    this.recentActivities = [
      {
        title: 'Leave Requests Pending',
        description: `${data.leaveStats.pendingRequests} leave requests awaiting approval`,
        time: 'Current',
        icon: 'event_available',
        type: 'pending'
      },
      {
        title: 'Performance Reviews Due',
        description: `${data.performanceStats.pendingReviews} performance reviews due this quarter`,
        time: 'Current',
        icon: 'star_rate',
        type: 'pending'
      },
      {
        title: 'Requests Processed',
        description: `${data.requestSummary.approvedRequests} requests approved this month`,
        time: 'This month',
        icon: 'check_circle',
        type: 'approved'
      }
    ];
  }

  getLeaveProgress(): number {
    const maxExpected = Math.max(20, this.hrMetrics.leaveRequests);
    return Math.min((this.hrMetrics.leaveRequests / maxExpected) * 100, 100);
  }

  getExpenseProgress(): number {
    const maxExpected = Math.max(15, this.hrMetrics.expenseReports);
    return Math.min((this.hrMetrics.expenseReports / maxExpected) * 100, 100);
  }

  getTrainingProgress(): number {
    const maxExpected = Math.max(10, this.hrMetrics.trainingRequests);
    return Math.min((this.hrMetrics.trainingRequests / maxExpected) * 100, 100);
  }

  getProcessingRate(): number {
    const total = this.hrMetrics.pendingRequests + this.hrMetrics.processedThisMonth;
    if (total === 0) return 0;
    return Math.round((this.hrMetrics.processedThisMonth / total) * 100);
  }

  getLeaveApprovalRate(): number {
    // This would ideally come from the leave statistics
    // For now, we'll calculate based on available data
    if (this.leaveStats) {
      const total = this.leaveStats.approvedRequests + this.leaveStats.rejectedRequests;
      if (total === 0) return 0;
      return Math.round((this.leaveStats.approvedRequests / total) * 100);
    }
    return 85; // Default fallback
  }

  getPerformanceCompletionRate(): number {
    // This would ideally come from the performance review statistics
    if (this.performanceStats) {
      const total = this.performanceStats.completedReviews + this.performanceStats.pendingReviews;
      if (total === 0) return 0;
      return Math.round((this.performanceStats.completedReviews / total) * 100);
    }
    return 75; // Default fallback
  }

  // Store the statistics for use in calculations
  private leaveStats?: LeaveStatistics;
  private performanceStats?: PerformanceReviewStatistics;
}