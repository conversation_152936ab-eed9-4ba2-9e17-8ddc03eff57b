<div class="manager-dashboard-container">
  <h2>Manager Dashboard</h2>
  <p>Welcome, Manager! Here you can review and validate pending requests.</p>

  <h3>Pending Requests</h3>
  <!-- Placeholder for a list of pending requests -->
  <div class="pending-requests-list">
    <p>No pending requests at the moment.</p>
    <!-- In a real application, this would be dynamically populated -->
    <!--
    <ul>
      <li *ngFor="let request of pendingRequests">
        <h4>{{ request.type }} from {{ request.employeeName }}</h4>
        <p>Status: {{ request.status }}</p>
        <button>View Details</button>
        <button>Approve</button>
        <button>Reject</button>
      </li>
    </ul>
    -->
  </div>

  <h3>Recently Approved/Rejected</h3>
  <!-- Placeholder for a list of recently approved/rejected requests -->
  <div class="recent-actions-list">
    <p>No recent actions.</p>
    <!-- In a real application, this would be dynamically populated -->
  </div>
</div>
