.login-card {
  width: 100%;
  border: none;
  box-shadow: none;
  background: transparent;
  
  mat-card-header {
    text-align: center;
    margin-bottom: 1.5rem;
    
    mat-card-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    mat-card-subtitle {
      color: #666;
      font-size: 0.9rem;
    }
  }
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  .full-width {
    width: 100%;
  }
  
  mat-form-field {
    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 0.5rem;
    }
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0 1.5rem 0;
  
  .forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
    
    &:hover {
      color: #764ba2;
      text-decoration: underline;
    }
  }
}

.login-button {
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  mat-spinner {
    margin-right: 8px;
  }
}

.card-actions {
  padding: 1rem 0 0 0;
  justify-content: center;
  
  .signup-text {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
    margin: 0;
    
    .signup-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
      
      &:hover {
        color: #764ba2;
        text-decoration: underline;
      }
    }
  }
}

// Custom snackbar styles
:host ::ng-deep {
  .success-snackbar {
    background-color: #4caf50;
    color: white;
  }
  
  .error-snackbar {
    background-color: #f44336;
    color: white;
  }
}

// Responsive Design
@media (max-width: 480px) {
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    
    .forgot-password {
      align-self: flex-end;
    }
  }
  
  .login-button {
    height: 44px;
    font-size: 0.9rem;
  }
}

// Animation for form elements
.login-form {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}