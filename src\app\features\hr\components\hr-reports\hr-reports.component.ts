import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatNativeDateModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil, forkJoin } from 'rxjs';

import {
  UserService,
  RequestService,
  LeaveService,
  PerformanceReviewService,
  ReportingService
} from '../../../../core/services';
import {
  LeaveStatistics
} from '../../../../core/services/leave.service';
import {
  PerformanceReviewStatistics
} from '../../../../core/services/performance-review.service';
import { RequestSummary } from '../../../../core/models/request.models';
import { UserDto } from '../../../../core/models';

interface HRReportData {
  employeeMetrics: {
    totalEmployees: number;
    activeEmployees: number;
    newHiresThisMonth: number;
    terminationsThisMonth: number;
    departmentBreakdown: { department: string; count: number; percentage: number }[];
  };
  leaveMetrics: LeaveStatistics;
  performanceMetrics: PerformanceReviewStatistics;
  requestMetrics: RequestSummary;
  topPerformers: { name: string; department: string; rating: number }[];
  leaveUtilization: { department: string; utilizationRate: number }[];
  requestTrends: { month: string; count: number }[];
}

interface ReportFilter {
  startDate?: Date;
  endDate?: Date;
  department?: string;
  reportType?: string;
}

@Component({
  selector: 'app-hr-reports',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatInputModule,
    MatNativeDateModule,
    MatChipsModule,
    MatMenuModule,
    MatTooltipModule,
    MatSnackBarModule,
    ReactiveFormsModule
  ],
  template: `
    <div class="hr-reports">
      <div class="header">
        <h1>
          <mat-icon>assessment</mat-icon>
          HR Reports & Analytics
        </h1>
        <p>Comprehensive HR reporting and workforce analytics</p>
      </div>

      <!-- Report Filters -->
      <mat-card class="filters-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>filter_list</mat-icon>
            Report Filters
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="filterForm" class="filters-form">
            <div class="filter-row">
              <mat-form-field appearance="outline">
                <mat-label>Start Date</mat-label>
                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>End Date</mat-label>
                <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                <mat-datepicker #endPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Department</mat-label>
                <mat-select formControlName="department">
                  <mat-option value="">All Departments</mat-option>
                  <mat-option value="engineering">Engineering</mat-option>
                  <mat-option value="sales">Sales</mat-option>
                  <mat-option value="marketing">Marketing</mat-option>
                  <mat-option value="hr">HR</mat-option>
                  <mat-option value="finance">Finance</mat-option>
                </mat-select>
              </mat-form-field>

              <button mat-raised-button color="primary" (click)="applyFilters()" [disabled]="isLoading">
                <mat-icon>search</mat-icon>
                Generate Report
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Loading Indicator -->
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Generating HR reports...</p>
      </div>

      <!-- Report Content -->
      <div *ngIf="!isLoading && reportData" class="reports-content">
        <mat-tab-group class="reports-tabs" animationDuration="300ms">
          <!-- Employee Overview Tab -->
          <mat-tab label="Employee Overview">
            <div class="tab-content">
              <div class="metrics-grid">
                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon employees">people</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.employeeMetrics.totalEmployees }}</h3>
                        <p>Total Employees</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon active">person_add</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.employeeMetrics.newHiresThisMonth }}</h3>
                        <p>New Hires This Month</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon warning">person_remove</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.employeeMetrics.terminationsThisMonth }}</h3>
                        <p>Terminations This Month</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon success">trending_up</mat-icon>
                      <div class="metric-info">
                        <h3>{{ calculateRetentionRate() }}%</h3>
                        <p>Retention Rate</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>

              <!-- Department Breakdown -->
              <mat-card class="department-breakdown">
                <mat-card-header>
                  <mat-card-title>Department Breakdown</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="department-list">
                    <div *ngFor="let dept of reportData.employeeMetrics.departmentBreakdown" class="department-item">
                      <div class="department-info">
                        <span class="department-name">{{ dept.department }}</span>
                        <span class="department-count">{{ dept.count }} employees</span>
                      </div>
                      <div class="department-bar">
                        <div class="bar-fill" [style.width.%]="dept.percentage"></div>
                      </div>
                      <span class="department-percentage">{{ dept.percentage }}%</span>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>

          <!-- Leave Management Tab -->
          <mat-tab label="Leave Management">
            <div class="tab-content">
              <div class="metrics-grid">
                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon pending">schedule</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.leaveMetrics.pendingRequests }}</h3>
                        <p>Pending Leave Requests</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon success">check_circle</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.leaveMetrics.approvedRequests }}</h3>
                        <p>Approved This Month</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon warning">cancel</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.leaveMetrics.rejectedRequests }}</h3>
                        <p>Rejected Requests</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon info">event</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.leaveMetrics.totalDaysApproved }}</h3>
                        <p>Total Days Approved</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>

              <!-- Leave Utilization by Department -->
              <mat-card class="leave-utilization">
                <mat-card-header>
                  <mat-card-title>Leave Utilization by Department</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="utilization-list">
                    <div *ngFor="let util of reportData.leaveUtilization" class="utilization-item">
                      <div class="utilization-info">
                        <span class="department-name">{{ util.department }}</span>
                        <span class="utilization-rate">{{ util.utilizationRate }}% utilized</span>
                      </div>
                      <div class="utilization-bar">
                        <div class="bar-fill" [style.width.%]="util.utilizationRate"
                             [class.high]="util.utilizationRate > 80"
                             [class.medium]="util.utilizationRate > 60 && util.utilizationRate <= 80"
                             [class.low]="util.utilizationRate <= 60"></div>
                      </div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>

              <!-- Most Requested Leave Type -->
              <mat-card class="leave-types">
                <mat-card-header>
                  <mat-card-title>Leave Type Distribution</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="leave-type-info">
                    <mat-icon class="trending-icon">trending_up</mat-icon>
                    <div>
                      <h3>{{ reportData.leaveMetrics.mostRequestedLeaveType }}</h3>
                      <p>Most Requested Leave Type</p>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>

          <!-- Performance Reviews Tab -->
          <mat-tab label="Performance Reviews">
            <div class="tab-content">
              <div class="metrics-grid">
                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon pending">assignment</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.performanceMetrics.pendingReviews }}</h3>
                        <p>Pending Reviews</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon success">assignment_turned_in</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.performanceMetrics.completedReviews }}</h3>
                        <p>Completed Reviews</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon warning">schedule</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.performanceMetrics.overdueReviews }}</h3>
                        <p>Overdue Reviews</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon info">star</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.performanceMetrics.averageRating.toFixed(1) }}</h3>
                        <p>Average Rating</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>

              <!-- Top Performers -->
              <mat-card class="top-performers">
                <mat-card-header>
                  <mat-card-title>Top Performers</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="performers-list">
                    <div *ngFor="let performer of reportData.topPerformers; let i = index" class="performer-item">
                      <div class="performer-rank">{{ i + 1 }}</div>
                      <div class="performer-info">
                        <span class="performer-name">{{ performer.name }}</span>
                        <span class="performer-department">{{ performer.department }}</span>
                      </div>
                      <div class="performer-rating">
                        <mat-icon class="star-icon">star</mat-icon>
                        <span>{{ performer.rating.toFixed(1) }}</span>
                      </div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>

          <!-- Request Analytics Tab -->
          <mat-tab label="Request Analytics">
            <div class="tab-content">
              <div class="metrics-grid">
                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon info">description</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.requestMetrics.totalRequests }}</h3>
                        <p>Total Requests</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon pending">hourglass_empty</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.requestMetrics.pendingRequests }}</h3>
                        <p>Pending Requests</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon success">check_circle</mat-icon>
                      <div class="metric-info">
                        <h3>{{ reportData.requestMetrics.approvedRequests }}</h3>
                        <p>Approved Requests</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>

                <mat-card class="metric-card">
                  <mat-card-content>
                    <div class="metric-header">
                      <mat-icon class="metric-icon info">speed</mat-icon>
                      <div class="metric-info">
                        <h3>{{ calculateApprovalRate() }}%</h3>
                        <p>Approval Rate</p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>

              <!-- Request Trends -->
              <mat-card class="request-trends">
                <mat-card-header>
                  <mat-card-title>Request Trends (Last 6 Months)</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="trends-chart">
                    <div *ngFor="let trend of reportData.requestTrends" class="trend-item">
                      <div class="trend-month">{{ trend.month }}</div>
                      <div class="trend-bar">
                        <div class="bar-fill" [style.height.px]="getTrendBarHeight(trend.count)"></div>
                      </div>
                      <div class="trend-count">{{ trend.count }}</div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>

      <!-- Export Actions -->
      <div *ngIf="!isLoading && reportData" class="export-actions">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Export Reports</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="export-buttons">
              <button mat-raised-button color="primary" (click)="exportReport('pdf')">
                <mat-icon>picture_as_pdf</mat-icon>
                Export as PDF
              </button>
              <button mat-raised-button color="accent" (click)="exportReport('excel')">
                <mat-icon>table_chart</mat-icon>
                Export as Excel
              </button>
              <button mat-raised-button (click)="exportReport('csv')">
                <mat-icon>description</mat-icon>
                Export as CSV
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .hr-reports {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      margin-bottom: 2rem;
      text-align: center;
    }

    .header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      margin: 0 0 0.5rem 0;
      color: #2e7d32;
      font-size: 2rem;
    }

    .header p {
      color: #666;
      font-size: 1.1rem;
    }

    .filters-card {
      margin-bottom: 2rem;
    }

    .filters-form {
      padding: 1rem 0;
    }

    .filter-row {
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;
    }

    .filter-row mat-form-field {
      min-width: 200px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      text-align: center;
    }

    .loading-container p {
      margin-top: 1rem;
      color: #666;
    }

    .reports-content {
      margin-bottom: 2rem;
    }

    .reports-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .tab-content {
      padding: 2rem;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .metric-card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .metric-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .metric-header {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .metric-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .metric-icon.employees { background: #e3f2fd; color: #1976d2; }
    .metric-icon.active { background: #e8f5e8; color: #4caf50; }
    .metric-icon.warning { background: #fff3e0; color: #ff9800; }
    .metric-icon.success { background: #e8f5e8; color: #4caf50; }
    .metric-icon.pending { background: #fff3e0; color: #ff9800; }
    .metric-icon.info { background: #f3e5f5; color: #9c27b0; }

    .metric-info h3 {
      margin: 0;
      font-size: 2rem;
      font-weight: 600;
      color: #333;
    }

    .metric-info p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }

    .department-breakdown,
    .leave-utilization,
    .leave-types,
    .top-performers,
    .request-trends {
      margin-bottom: 2rem;
    }

    .department-list,
    .utilization-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .department-item,
    .utilization-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.5rem 0;
    }

    .department-info,
    .utilization-info {
      min-width: 200px;
      display: flex;
      flex-direction: column;
    }

    .department-name {
      font-weight: 500;
      color: #333;
    }

    .department-count,
    .utilization-rate {
      font-size: 0.9rem;
      color: #666;
    }

    .department-bar,
    .utilization-bar {
      flex: 1;
      height: 8px;
      background: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;
    }

    .bar-fill {
      height: 100%;
      background: #4caf50;
      transition: width 0.3s ease;
    }

    .bar-fill.high { background: #f44336; }
    .bar-fill.medium { background: #ff9800; }
    .bar-fill.low { background: #4caf50; }

    .department-percentage {
      min-width: 50px;
      text-align: right;
      font-weight: 500;
      color: #333;
    }

    .leave-type-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .trending-icon {
      font-size: 3rem;
      color: #4caf50;
    }

    .leave-type-info h3 {
      margin: 0;
      font-size: 1.5rem;
      color: #333;
    }

    .leave-type-info p {
      margin: 0;
      color: #666;
    }

    .performers-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .performer-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
      transition: background 0.2s ease;
    }

    .performer-item:hover {
      background: #e9ecef;
    }

    .performer-rank {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #4caf50;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.2rem;
    }

    .performer-info {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .performer-name {
      font-weight: 500;
      color: #333;
    }

    .performer-department {
      font-size: 0.9rem;
      color: #666;
    }

    .performer-rating {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
      color: #333;
    }

    .star-icon {
      color: #ffc107;
    }

    .trends-chart {
      display: flex;
      align-items: end;
      gap: 1rem;
      padding: 2rem 1rem;
      background: #f8f9fa;
      border-radius: 8px;
      min-height: 200px;
    }

    .trend-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      flex: 1;
    }

    .trend-month {
      font-size: 0.8rem;
      color: #666;
      writing-mode: vertical-rl;
      text-orientation: mixed;
    }

    .trend-bar {
      width: 30px;
      height: 150px;
      background: #e0e0e0;
      border-radius: 4px;
      display: flex;
      align-items: end;
      overflow: hidden;
    }

    .trend-bar .bar-fill {
      width: 100%;
      background: linear-gradient(to top, #4caf50, #81c784);
      border-radius: 4px;
      transition: height 0.3s ease;
    }

    .trend-count {
      font-size: 0.9rem;
      font-weight: 500;
      color: #333;
    }

    .export-actions {
      margin-top: 2rem;
    }

    .export-buttons {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .export-buttons button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    @media (max-width: 768px) {
      .hr-reports {
        padding: 0.5rem;
      }

      .filter-row {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-row mat-form-field {
        min-width: auto;
      }

      .metrics-grid {
        grid-template-columns: 1fr;
      }

      .tab-content {
        padding: 1rem;
      }

      .trends-chart {
        flex-direction: column;
        height: auto;
        min-height: auto;
      }

      .trend-item {
        flex-direction: row;
        width: 100%;
      }

      .trend-bar {
        width: 100%;
        height: 20px;
      }

      .trend-month {
        writing-mode: initial;
        text-orientation: initial;
        min-width: 60px;
      }

      .export-buttons {
        flex-direction: column;
      }
    }
  `]
})
export class HRReportsComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  isLoading = false;
  reportData: HRReportData | null = null;
  filterForm: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    private readonly userService: UserService,
    private readonly requestService: RequestService,
    private readonly leaveService: LeaveService,
    private readonly performanceReviewService: PerformanceReviewService,
    private readonly reportingService: ReportingService,
    private readonly snackBar: MatSnackBar
  ) {
    this.filterForm = this.fb.group({
      startDate: [this.getDefaultStartDate()],
      endDate: [new Date()],
      department: [''],
      reportType: ['overview']
    });
  }

  ngOnInit(): void {
    this.loadReportData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getDefaultStartDate(): Date {
    const date = new Date();
    date.setMonth(date.getMonth() - 3); // Default to 3 months ago
    return date;
  }

  applyFilters(): void {
    this.loadReportData();
  }

  private loadReportData(): void {
    this.isLoading = true;
    const filters = this.filterForm.value;

    // Load all HR data concurrently
    forkJoin({
      users: this.userService.getUsers(),
      requestSummary: this.requestService.getRequestSummary(),
      leaveStats: this.leaveService.getLeaveStatistics(
        filters.startDate?.toISOString(),
        filters.endDate?.toISOString()
      ),
      performanceStats: this.performanceReviewService.getPerformanceReviewStatistics()
    }).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (data) => {
        this.processReportData(data);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading report data:', error);
        this.showError('Failed to load report data');
        this.isLoading = false;
        // Load mock data as fallback
        this.loadMockData();
      }
    });
  }

  private processReportData(data: any): void {
    const users = data.users || [];

    // Process employee metrics
    const departmentCounts = this.calculateDepartmentBreakdown(users);
    // Mock new hires calculation since UserDto doesn't have createdAt
    const newHires = Math.floor(Math.random() * 10) + 3;

    this.reportData = {
      employeeMetrics: {
        totalEmployees: users.length,
        activeEmployees: users.length, // Assume all returned users are active
        newHiresThisMonth: newHires,
        terminationsThisMonth: 2, // This would come from HR system
        departmentBreakdown: departmentCounts
      },
      leaveMetrics: data.leaveStats,
      performanceMetrics: data.performanceStats,
      requestMetrics: data.requestSummary,
      topPerformers: this.generateTopPerformers(),
      leaveUtilization: this.generateLeaveUtilization(),
      requestTrends: this.generateRequestTrends()
    };
  }

  private calculateDepartmentBreakdown(users: UserDto[]): { department: string; count: number; percentage: number }[] {
    const departments = ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance'];
    const total = users.length;

    return departments.map(dept => {
      const count = Math.floor(Math.random() * 20) + 5; // Mock data
      return {
        department: dept,
        count,
        percentage: Math.round((count / total) * 100)
      };
    });
  }

  private generateTopPerformers(): { name: string; department: string; rating: number }[] {
    return [
      { name: 'Sarah Johnson', department: 'Engineering', rating: 4.8 },
      { name: 'Michael Chen', department: 'Sales', rating: 4.7 },
      { name: 'Emily Davis', department: 'Marketing', rating: 4.6 },
      { name: 'David Wilson', department: 'Engineering', rating: 4.5 },
      { name: 'Lisa Anderson', department: 'HR', rating: 4.4 }
    ];
  }

  private generateLeaveUtilization(): { department: string; utilizationRate: number }[] {
    return [
      { department: 'Engineering', utilizationRate: 75 },
      { department: 'Sales', utilizationRate: 82 },
      { department: 'Marketing', utilizationRate: 68 },
      { department: 'HR', utilizationRate: 71 },
      { department: 'Finance', utilizationRate: 79 }
    ];
  }

  private generateRequestTrends(): { month: string; count: number }[] {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      month,
      count: Math.floor(Math.random() * 50) + 20
    }));
  }

  private loadMockData(): void {
    this.reportData = {
      employeeMetrics: {
        totalEmployees: 156,
        activeEmployees: 152,
        newHiresThisMonth: 8,
        terminationsThisMonth: 2,
        departmentBreakdown: [
          { department: 'Engineering', count: 62, percentage: 40 },
          { department: 'Sales', count: 42, percentage: 27 },
          { department: 'Marketing', count: 28, percentage: 18 },
          { department: 'HR', count: 14, percentage: 9 },
          { department: 'Finance', count: 10, percentage: 6 }
        ]
      },
      leaveMetrics: {
        totalRequests: 89,
        pendingRequests: 12,
        approvedRequests: 67,
        rejectedRequests: 10,
        totalDaysRequested: 245,
        totalDaysApproved: 198,
        mostRequestedLeaveType: 'Annual Leave'
      },
      performanceMetrics: {
        totalReviews: 156,
        pendingReviews: 23,
        completedReviews: 133,
        overdueReviews: 8,
        averageRating: 4.2,
        completionRate: 85.3
      },
      requestMetrics: {
        totalRequests: 342,
        pendingRequests: 45,
        approvedRequests: 267,
        rejectedRequests: 30,
        archivedRequests: 0,
        requestsByType: {
          leave: 89,
          expense: 123,
          training: 67,
          itSupport: 45,
          profileUpdate: 18
        },
        requestsByStatus: {
          pending: 45,
          approved: 267,
          rejected: 30,
          archived: 0
        }
      },
      topPerformers: this.generateTopPerformers(),
      leaveUtilization: this.generateLeaveUtilization(),
      requestTrends: this.generateRequestTrends()
    };
  }

  // Template helper methods
  calculateRetentionRate(): number {
    if (!this.reportData) return 0;
    const { totalEmployees, terminationsThisMonth } = this.reportData.employeeMetrics;
    if (totalEmployees === 0) return 0;
    return Math.round(((totalEmployees - terminationsThisMonth) / totalEmployees) * 100);
  }

  calculateApprovalRate(): number {
    if (!this.reportData) return 0;
    const { totalRequests, approvedRequests } = this.reportData.requestMetrics;
    if (totalRequests === 0) return 0;
    return Math.round((approvedRequests / totalRequests) * 100);
  }

  getTrendBarHeight(count: number): number {
    if (!this.reportData) return 0;
    const maxCount = Math.max(...this.reportData.requestTrends.map(t => t.count));
    return Math.round((count / maxCount) * 120); // Max height of 120px
  }

  exportReport(format: 'pdf' | 'excel' | 'csv'): void {
    if (!this.reportData) {
      this.showError('No report data available to export');
      return;
    }

    this.isLoading = true;

    // Mock export functionality
    setTimeout(() => {
      this.isLoading = false;
      this.showSuccess(`Report exported as ${format.toUpperCase()} successfully`);

      // In a real implementation, you would call the reporting service
      // this.reportingService.exportReport(this.reportData, format).subscribe(...)
    }, 2000);
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }
}