.sidenav-container {
  height: 100vh;
}

.sidenav {
  width: 280px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .sidenav-header {
    padding: 24px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    .logo-container {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      
      .logo-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
        margin-right: 12px;
        color: white;
      }
      
      .logo-text {
        font-size: 24px;
        font-weight: 600;
        color: white;
      }
    }
    
    .user-info {
      display: flex;
      align-items: center;
      
      .user-avatar {
        margin-right: 12px;
        
        mat-icon {
          font-size: 40px;
          width: 40px;
          height: 40px;
          color: rgba(255, 255, 255, 0.8);
        }
      }
      
      .user-details {
        .user-name {
          font-weight: 500;
          font-size: 14px;
          color: white;
          margin-bottom: 2px;
        }
        
        .user-role {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }
  
  .nav-list {
    padding: 0;
    flex: 1;
    
    .nav-item {
      color: rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;
      margin: 4px 8px;
      border-radius: 8px;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
      }
      
      &.active-link {
        background-color: rgba(255, 255, 255, 0.15);
        color: white;
        
        mat-icon {
          color: white;
        }
      }
      
      mat-icon {
        color: rgba(255, 255, 255, 0.7);
        margin-right: 16px;
      }
    }
  }
  
  .sidenav-footer {
    margin-top: auto;
    padding: 16px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    
    .nav-item {
      color: rgba(255, 255, 255, 0.8);
      margin: 4px 8px;
      border-radius: 8px;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
      }
      
      mat-icon {
        color: rgba(255, 255, 255, 0.7);
        margin-right: 16px;
      }
    }
  }
}

.toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white !important;
  color: #333 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .toolbar-title {
    font-size: 20px;
    font-weight: 500;
    color: #333;
  }
  
  .toolbar-spacer {
    flex: 1 1 auto;
  }
  
  button {
    color: #333;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

.main-content {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background-color: #f8f9fa;
}

// Notification Menu Styles
.notification-menu {
  width: 400px;
  max-height: 500px;
  
  .notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    
    button {
      font-size: 12px;
      color: #667eea;
    }
  }
  
  .notification-list {
    max-height: 300px;
    overflow-y: auto;
    
    .notification-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      &.unread {
        background-color: #e3f2fd;
        
        &:hover {
          background-color: #bbdefb;
        }
      }
      
      .notification-time {
        font-size: 11px;
        color: #666;
        margin-top: 4px;
      }
    }
  }
  
  .no-notifications {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px 16px;
    color: #666;
    
    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 8px;
      opacity: 0.5;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .sidenav {
    width: 100%;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .notification-menu {
    width: 320px;
  }
}

// Dark mode support (optional)
@media (prefers-color-scheme: dark) {
  .toolbar {
    background: #1e1e1e !important;
    color: white !important;
    
    .toolbar-title {
      color: white;
    }
    
    button {
      color: white;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  .main-content {
    background-color: #2D1E3E  ;
    color: white;
  }
}

// Animation for smooth transitions
.nav-item {
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.notification-item {
  transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

// Custom scrollbar for notification list
.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notification-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}