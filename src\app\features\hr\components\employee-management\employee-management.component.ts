import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';

interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  department: string;
  position: string;
  hireDate: Date;
  status: 'Active' | 'Inactive' | 'On Leave';
  manager: string;
  phoneNumber?: string;
  emergencyContact?: string;
}

@Component({
  selector: 'app-employee-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatChipsModule,
    MatPaginatorModule,
    MatTooltipModule,
    MatTabsModule,
    MatMenuModule,
    MatDividerModule
  ],
  template: `
    <div class="employee-management">
      <div class="header">
        <h1>
          <mat-icon>badge</mat-icon>
          Employee Management
        </h1>
        <p>Manage employee records and information</p>
      </div>

      <mat-card class="filters-card">
        <mat-card-content>
          <div class="filters-row">
            <mat-form-field appearance="outline">
              <mat-label>Search Employees</mat-label>
              <input matInput [(ngModel)]="searchTerm" (ngModelChange)="applyFilters()" placeholder="Name, email, or ID">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Department</mat-label>
              <mat-select [(ngModel)]="selectedDepartment" (selectionChange)="applyFilters()">
                <mat-option value="">All Departments</mat-option>
                <mat-option *ngFor="let dept of departments" [value]="dept">{{dept}}</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilters()">
                <mat-option value="">All Status</mat-option>
                <mat-option value="Active">Active</mat-option>
                <mat-option value="Inactive">Inactive</mat-option>
                <mat-option value="On Leave">On Leave</mat-option>
              </mat-select>
            </mat-form-field>

            <button mat-raised-button color="primary" (click)="addEmployee()">
              <mat-icon>person_add</mat-icon>
              Add Employee
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="employees-table-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>people</mat-icon>
            Employee Directory ({{filteredEmployees.length}})
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="filteredEmployees" class="employees-table">
              <!-- Avatar Column -->
              <ng-container matColumnDef="avatar">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let employee">
                  <div class="employee-avatar">
                    {{getInitials(employee.firstName, employee.lastName)}}
                  </div>
                </td>
              </ng-container>

              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let employee">
                  <div class="employee-info">
                    <strong>{{employee.firstName}} {{employee.lastName}}</strong>
                    <small>{{employee.email}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Department Column -->
              <ng-container matColumnDef="department">
                <th mat-header-cell *matHeaderCellDef>Department</th>
                <td mat-cell *matCellDef="let employee">
                  <div class="department-info">
                    <span class="department">{{employee.department}}</span>
                    <small class="position">{{employee.position}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Manager Column -->
              <ng-container matColumnDef="manager">
                <th mat-header-cell *matHeaderCellDef>Manager</th>
                <td mat-cell *matCellDef="let employee">{{employee.manager}}</td>
              </ng-container>

              <!-- Hire Date Column -->
              <ng-container matColumnDef="hireDate">
                <th mat-header-cell *matHeaderCellDef>Hire Date</th>
                <td mat-cell *matCellDef="let employee">{{employee.hireDate | date:'MMM dd, yyyy'}}</td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let employee">
                  <mat-chip [class]="getStatusClass(employee.status)">
                    {{employee.status}}
                  </mat-chip>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let employee">
                  <div class="action-buttons">
                    <button mat-icon-button (click)="viewEmployee(employee)" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button (click)="editEmployee(employee)" matTooltip="Edit Employee">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button (click)="viewRequests(employee)" matTooltip="View Requests">
                      <mat-icon>assignment</mat-icon>
                    </button>
                    <button mat-icon-button [matMenuTriggerFor]="actionMenu" matTooltip="More Actions">
                      <mat-icon>more_vert</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="employee-row"></tr>
            </table>
          </div>

          <mat-paginator 
            [length]="filteredEmployees.length"
            [pageSize]="pageSize"
            [pageSizeOptions]="[10, 25, 50, 100]"
            showFirstLastButtons>
          </mat-paginator>
        </mat-card-content>
      </mat-card>

      <!-- Department Statistics -->
      <mat-card class="stats-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>pie_chart</mat-icon>
            Department Statistics
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stats-grid">
            <div *ngFor="let stat of departmentStats" class="stat-item">
              <div class="stat-header">
                <h4>{{stat.department}}</h4>
                <span class="stat-count">{{stat.count}} employees</span>
              </div>
              <div class="stat-bar">
                <div class="stat-fill" [style.width.%]="stat.percentage"></div>
              </div>
              <div class="stat-details">
                <small>{{stat.percentage}}% of total workforce</small>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Action Menu -->
    <mat-menu #actionMenu="matMenu">
      <button mat-menu-item (click)="changeStatus()">
        <mat-icon>swap_horiz</mat-icon>
        <span>Change Status</span>
      </button>
      <button mat-menu-item (click)="assignManager()">
        <mat-icon>supervisor_account</mat-icon>
        <span>Assign Manager</span>
      </button>
      <button mat-menu-item (click)="generateReport()">
        <mat-icon>description</mat-icon>
        <span>Generate Report</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item (click)="deactivateEmployee()" class="warn-action">
        <mat-icon>person_off</mat-icon>
        <span>Deactivate</span>
      </button>
    </mat-menu>
  `,
  styles: [`
    .employee-management {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      margin-bottom: 2rem;
      text-align: center;
    }

    .header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      margin: 0 0 0.5rem 0;
      color: #2e7d32;
      font-size: 2rem;
    }

    .header p {
      color: #666;
      font-size: 1.1rem;
      margin: 0;
    }

    .filters-card {
      margin-bottom: 1rem;
    }

    .filters-row {
      display: flex;
      gap: 1rem;
      align-items: flex-end;
      flex-wrap: wrap;
    }

    .filters-row mat-form-field {
      min-width: 200px;
    }

    .employees-table-card {
      margin-bottom: 2rem;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .table-container {
      overflow-x: auto;
    }

    .employees-table {
      width: 100%;
    }

    .employee-row:hover {
      background-color: #f5f5f5;
    }

    .employee-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #4caf50;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.9rem;
    }

    .employee-info {
      display: flex;
      flex-direction: column;
    }

    .employee-info strong {
      font-size: 0.95rem;
    }

    .employee-info small {
      color: #666;
      font-size: 0.8rem;
    }

    .department-info {
      display: flex;
      flex-direction: column;
    }

    .department {
      font-weight: 500;
    }

    .position {
      color: #666;
      font-size: 0.8rem;
    }

    .status-active {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-inactive {
      background-color: #ffebee;
      color: #c62828;
    }

    .status-on-leave {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
    }

    .stats-card {
      margin-bottom: 2rem;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;
    }

    .stat-item {
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 8px;
    }

    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .stat-header h4 {
      margin: 0;
      color: #333;
    }

    .stat-count {
      color: #666;
      font-size: 0.9rem;
    }

    .stat-bar {
      height: 8px;
      background-color: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 0.5rem;
    }

    .stat-fill {
      height: 100%;
      background-color: #4caf50;
      transition: width 0.3s ease;
    }

    .stat-details small {
      color: #666;
      font-size: 0.8rem;
    }

    .warn-action {
      color: #f44336;
    }

    @media (max-width: 768px) {
      .filters-row {
        flex-direction: column;
        align-items: stretch;
      }

      .filters-row mat-form-field {
        min-width: 100%;
      }

      .action-buttons {
        flex-direction: column;
      }

      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class EmployeeManagementComponent implements OnInit {
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  displayedColumns: string[] = ['avatar', 'name', 'department', 'manager', 'hireDate', 'status', 'actions'];
  
  searchTerm = '';
  selectedDepartment = '';
  selectedStatus = '';
  pageSize = 25;

  departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations'];
  
  departmentStats = [
    { department: 'Engineering', count: 15, percentage: 33 },
    { department: 'Marketing', count: 8, percentage: 18 },
    { department: 'Sales', count: 10, percentage: 22 },
    { department: 'HR', count: 4, percentage: 9 },
    { department: 'Finance', count: 5, percentage: 11 },
    { department: 'Operations', count: 3, percentage: 7 }
  ];

  ngOnInit(): void {
    this.loadEmployees();
  }

  loadEmployees(): void {
    // Mock data
    this.employees = [
      {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        department: 'Engineering',
        position: 'Senior Developer',
        hireDate: new Date('2022-03-15'),
        status: 'Active',
        manager: 'Jane Smith',
        phoneNumber: '******-0123'
      },
      {
        id: '2',
        firstName: 'Sarah',
        lastName: 'Wilson',
        email: '<EMAIL>',
        department: 'Marketing',
        position: 'Marketing Manager',
        hireDate: new Date('2021-08-20'),
        status: 'Active',
        manager: 'Mike Johnson'
      },
      {
        id: '3',
        firstName: 'Mike',
        lastName: 'Johnson',
        email: '<EMAIL>',
        department: 'Sales',
        position: 'Sales Director',
        hireDate: new Date('2020-01-10'),
        status: 'On Leave',
        manager: 'CEO'
      }
    ];
    this.applyFilters();
  }

  applyFilters(): void {
    this.filteredEmployees = this.employees.filter(employee => {
      const matchesSearch = !this.searchTerm || 
        employee.firstName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        employee.lastName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        employee.email.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesDepartment = !this.selectedDepartment || employee.department === this.selectedDepartment;
      const matchesStatus = !this.selectedStatus || employee.status === this.selectedStatus;
      
      return matchesSearch && matchesDepartment && matchesStatus;
    });
  }

  getInitials(firstName: string, lastName: string): string {
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
  }

  getStatusClass(status: string): string {
    return `status-${status.toLowerCase().replace(' ', '-')}`;
  }

  addEmployee(): void {
    console.log('Add employee dialog would open');
  }

  viewEmployee(employee: Employee): void {
    console.log('View employee:', employee);
  }

  editEmployee(employee: Employee): void {
    console.log('Edit employee:', employee);
  }

  viewRequests(employee: Employee): void {
    console.log('View requests for:', employee);
  }

  changeStatus(): void {
    console.log('Change status');
  }

  assignManager(): void {
    console.log('Assign manager');
  }

  generateReport(): void {
    console.log('Generate report');
  }

  deactivateEmployee(): void {
    console.log('Deactivate employee');
  }
}