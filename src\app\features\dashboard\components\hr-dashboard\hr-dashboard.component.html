<div class="hr-dashboard-container">
  <h2>HR Dashboard</h2>
  <p>Welcome, HR! Here you can process, archive, and track requests.</p>

  <h3>Requests for Processing</h3>
  <!-- Placeholder for a list of requests awaiting HR processing -->
  <div class="requests-processing-list">
    <p>No requests currently require HR processing.</p>
    <!-- In a real application, this would be dynamically populated -->
    <!--
    <ul>
      <li *ngFor="let request of requestsForProcessing">
        <h4>{{ request.type }} from {{ request.employeeName }}</h4>
        <p>Status: {{ request.status }}</p>
        <button>View Details</button>
        <button>Process</button>
        <button>Archive</button>
      </li>
    </ul>
    -->
  </div>

  <h3>Archived Requests</h3>
  <!-- Placeholder for a list of archived requests -->
  <div class="archived-requests-list">
    <p>No archived requests.</p>
    <!-- In a real application, this would be dynamically populated -->
  </div>
</div>
