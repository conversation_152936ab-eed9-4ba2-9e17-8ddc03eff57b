{"name": "bpm-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:BPM-Frontend": "node dist/bpm-frontend/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/cdk": "^19.2.19", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/material": "^19.2.19", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.15", "@mdi/font": "^7.4.47", "@microsoft/signalr": "^9.0.6", "chart.js": "^4.5.0", "express": "^4.18.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "ng2-charts": "^8.0.0", "primeicons": "^7.0.0", "primeng": "^17.18.15", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular/cli": "^19.2.15", "@angular/compiler-cli": "^19.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}