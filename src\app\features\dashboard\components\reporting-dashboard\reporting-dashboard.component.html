<div class="reporting-dashboard-container">
  <!-- Header Section -->
  <div class="dashboard-header">
    <h1>Admin Dashboard</h1>
    <p>Welcome back, {{currentUser?.userName}}! Here's your system overview.</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading dashboard data...</p>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading" class="dashboard-content">

    <!-- System Overview Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card">
        <mat-card-header>
          <mat-icon mat-card-avatar class="users-icon">people</mat-icon>
          <mat-card-title>Total Users</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{systemStats.totalUsers}}</div>
          <div class="stat-label">Registered Users</div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-icon mat-card-avatar class="workflows-icon">account_tree</mat-icon>
          <mat-card-title>Active Workflows</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{systemStats.activeWorkflows}}</div>
          <div class="stat-label">Running Processes</div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-icon mat-card-avatar class="requests-icon">assignment</mat-icon>
          <mat-card-title>Total Requests</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{systemStats.totalRequests}}</div>
          <div class="stat-label">All Time</div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-icon mat-card-avatar class="pending-icon">pending_actions</mat-icon>
          <mat-card-title>Pending Approvals</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{systemStats.pendingApprovals}}</div>
          <div class="stat-label">Awaiting Action</div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Admin Actions -->
    <div class="admin-actions">
      <h2>Admin Actions</h2>
      <div class="action-cards">
        <mat-card class="action-card" (click)="navigateTo('/admin/users')">
          <mat-card-header>
            <mat-icon mat-card-avatar>manage_accounts</mat-icon>
            <mat-card-title>User Management</mat-card-title>
            <mat-card-subtitle>Manage users, roles, and permissions</mat-card-subtitle>
          </mat-card-header>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              Manage Users
            </button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="action-card" (click)="navigateTo('/admin/workflows')">
          <mat-card-header>
            <mat-icon mat-card-avatar>settings</mat-icon>
            <mat-card-title>Workflow Designer</mat-card-title>
            <mat-card-subtitle>Create and configure workflows</mat-card-subtitle>
          </mat-card-header>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              Design Workflows
            </button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="action-card" (click)="navigateTo('/admin/reports')">
          <mat-card-header>
            <mat-icon mat-card-avatar>analytics</mat-icon>
            <mat-card-title>System Reports</mat-card-title>
            <mat-card-subtitle>Generate detailed system reports</mat-card-subtitle>
          </mat-card-header>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              View Reports
            </button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="action-card" (click)="navigateTo('/admin/settings')">
          <mat-card-header>
            <mat-icon mat-card-avatar>tune</mat-icon>
            <mat-card-title>System Settings</mat-card-title>
            <mat-card-subtitle>Configure system preferences</mat-card-subtitle>
          </mat-card-header>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              Settings
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>

    <!-- Quick Reports Section -->
    <div class="quick-reports">
      <h2>Quick Reports</h2>
      <mat-card>
        <mat-card-header>
          <mat-card-title>Generate Reports</mat-card-title>
          <mat-card-subtitle>Export system data and analytics</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="report-buttons">
            <button mat-raised-button color="primary">
              <mat-icon>picture_as_pdf</mat-icon>
              Export to PDF
            </button>
            <button mat-raised-button color="accent">
              <mat-icon>table_chart</mat-icon>
              Export to Excel
            </button>
            <button mat-raised-button>
              <mat-icon>bar_chart</mat-icon>
              Analytics Report
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

  </div>
</div>
