<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h1>BPM Light</h1>
      <p>Sign in to your account</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <div class="form-group">
        <label for="userName">Username or Email</label>
        <input
          type="text"
          id="userName"
          formControlName="userName"
          class="form-control"
          [class.is-invalid]="isFieldInvalid('userName')"
          placeholder="Enter your username or email"
        />
        <div class="invalid-feedback" *ngIf="isFieldInvalid('userName')">
          {{ getFieldError('userName') }}
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          class="form-control"
          [class.is-invalid]="isFieldInvalid('password')"
          placeholder="Enter your password"
        />
        <div class="invalid-feedback" *ngIf="isFieldInvalid('password')">
          {{ getFieldError('password') }}
        </div>
      </div>

      <div class="form-group form-check">
        <input
          type="checkbox"
          id="rememberMe"
          formControlName="rememberMe"
          class="form-check-input"
        />
        <label for="rememberMe" class="form-check-label">Remember me</label>
      </div>

      <div class="error-message" *ngIf="errorMessage">
        <i class="fas fa-exclamation-triangle"></i>
        {{ errorMessage }}
      </div>

      <button
        type="submit"
        class="btn btn-primary btn-block"
        [disabled]="isLoading"
      >
        <span *ngIf="isLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
        {{ isLoading ? 'Signing in...' : 'Sign In' }}
      </button>
    </form>

    <div class="login-footer">
      <p>Don't have an account? <a routerLink="/auth/register">Sign up</a></p>
    </div>
  </div>
</div>
