import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { Subject, takeUntil } from 'rxjs';

import { AuthService } from '../../../../core/services/auth.service';
import { UserService } from '../../../../core/services/user.service';
import { UserDto } from '../../../../core/models/auth.models';

@Component({
  selector: 'app-user-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule
  ],
  template: `
    <div class="profile-container">
      <div class="profile-header">
        <h1>User Profile</h1>
        <p>Manage your account information and preferences</p>
      </div>

      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading profile...</p>
      </div>

      <div *ngIf="!isLoading" class="profile-content">
        <mat-tab-group>
          <!-- Personal Information Tab -->
          <mat-tab label="Personal Information">
            <div class="tab-content">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>Personal Details</mat-card-title>
                  <mat-card-subtitle>Update your personal information</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <form [formGroup]="profileForm" (ngSubmit)="updateProfile()">
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>First Name</mat-label>
                        <input matInput formControlName="firstName" placeholder="Enter your first name">
                        <mat-error *ngIf="profileForm.get('firstName')?.hasError('required')">
                          First name is required
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Last Name</mat-label>
                        <input matInput formControlName="lastName" placeholder="Enter your last name">
                        <mat-error *ngIf="profileForm.get('lastName')?.hasError('required')">
                          Last name is required
                        </mat-error>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Username</mat-label>
                        <input matInput formControlName="userName" placeholder="Enter your username">
                        <mat-error *ngIf="profileForm.get('userName')?.hasError('required')">
                          Username is required
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Email</mat-label>
                        <input matInput type="email" formControlName="email" placeholder="Enter your email">
                        <mat-error *ngIf="profileForm.get('email')?.hasError('required')">
                          Email is required
                        </mat-error>
                        <mat-error *ngIf="profileForm.get('email')?.hasError('email')">
                          Please enter a valid email
                        </mat-error>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Phone Number</mat-label>
                        <input matInput formControlName="phoneNumber" placeholder="Enter your phone number">
                      </mat-form-field>
                    </div>

                    <div class="user-roles">
                      <h3>Your Roles</h3>
                      <mat-chip-set>
                        <mat-chip *ngFor="let role of currentUser?.roles || []">
                          {{role}}
                        </mat-chip>
                      </mat-chip-set>
                    </div>

                    <mat-card-actions>
                      <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid || isUpdating">
                        <mat-icon>save</mat-icon>
                        <span *ngIf="!isUpdating">Update Profile</span>
                        <span *ngIf="isUpdating">Updating...</span>
                      </button>
                      <button mat-button type="button" (click)="resetForm()">
                        <mat-icon>refresh</mat-icon>
                        Reset
                      </button>
                    </mat-card-actions>
                  </form>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>

          <!-- Change Password Tab -->
          <mat-tab label="Change Password">
            <div class="tab-content">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>Change Password</mat-card-title>
                  <mat-card-subtitle>Update your account password</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <form [formGroup]="passwordForm" (ngSubmit)="changePassword()">
                    <mat-form-field appearance="outline">
                      <mat-label>Current Password</mat-label>
                      <input matInput type="password" formControlName="currentPassword" placeholder="Enter current password">
                      <mat-error *ngIf="passwordForm.get('currentPassword')?.hasError('required')">
                        Current password is required
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>New Password</mat-label>
                      <input matInput type="password" formControlName="newPassword" placeholder="Enter new password">
                      <mat-error *ngIf="passwordForm.get('newPassword')?.hasError('required')">
                        New password is required
                      </mat-error>
                      <mat-error *ngIf="passwordForm.get('newPassword')?.hasError('minlength')">
                        Password must be at least 6 characters
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Confirm New Password</mat-label>
                      <input matInput type="password" formControlName="confirmPassword" placeholder="Confirm new password">
                      <mat-error *ngIf="passwordForm.get('confirmPassword')?.hasError('required')">
                        Please confirm your password
                      </mat-error>
                      <mat-error *ngIf="passwordForm.hasError('passwordMismatch')">
                        Passwords do not match
                      </mat-error>
                    </mat-form-field>

                    <mat-card-actions>
                      <button mat-raised-button color="primary" type="submit" [disabled]="passwordForm.invalid || isChangingPassword">
                        <mat-icon>lock</mat-icon>
                        <span *ngIf="!isChangingPassword">Change Password</span>
                        <span *ngIf="isChangingPassword">Changing...</span>
                      </button>
                      <button mat-button type="button" (click)="resetPasswordForm()">
                        <mat-icon>refresh</mat-icon>
                        Reset
                      </button>
                    </mat-card-actions>
                  </form>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>

          <!-- Account Information Tab -->
          <mat-tab label="Account Information">
            <div class="tab-content">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>Account Details</mat-card-title>
                  <mat-card-subtitle>View your account information</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="account-info">
                    <div class="info-item">
                      <strong>User ID:</strong>
                      <span>{{currentUser?.id}}</span>
                    </div>
                    <mat-divider></mat-divider>
                    
                    <div class="info-item">
                      <strong>Username:</strong>
                      <span>{{currentUser?.userName}}</span>
                    </div>
                    <mat-divider></mat-divider>
                    
                    <div class="info-item">
                      <strong>Email:</strong>
                      <span>{{currentUser?.email}}</span>
                    </div>
                    <mat-divider></mat-divider>
                    
                    <div class="info-item">
                      <strong>Roles:</strong>
                      <span>{{(currentUser?.roles || []).join(', ')}}</span>
                    </div>
                    <mat-divider></mat-divider>
                    
                    <div class="info-item">
                      <strong>Account Status:</strong>
                      <span class="status-active">Active</span>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  `,
  styles: [`
    .profile-container {
      padding: 2rem;
      max-width: 1000px;
      margin: 0 auto;
    }

    .profile-header {
      margin-bottom: 2rem;
      text-align: center;
      
      h1 {
        color: #1976d2;
        margin-bottom: 0.5rem;
      }
      
      p {
        color: #666;
        font-size: 1.1rem;
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 4rem;
      
      p {
        margin-top: 1rem;
        color: #666;
      }
    }

    .profile-content {
      .mat-tab-group {
        .mat-tab-body-content {
          padding: 0;
        }
      }
    }

    .tab-content {
      padding: 2rem;
    }

    .form-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
      
      mat-form-field {
        flex: 1;
      }
    }

    .user-roles {
      margin: 2rem 0;
      
      h3 {
        color: #333;
        margin-bottom: 1rem;
      }
      
      mat-chip {
        margin-right: 0.5rem;
      }
    }

    .account-info {
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        
        strong {
          color: #333;
        }
        
        span {
          color: #666;
        }
        
        .status-active {
          color: #4caf50;
          font-weight: 500;
        }
      }
    }

    mat-card-actions {
      padding-top: 1rem;
      
      button {
        margin-right: 1rem;
        
        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }

    @media (max-width: 768px) {
      .profile-container {
        padding: 1rem;
      }
      
      .form-row {
        flex-direction: column;
        gap: 0;
      }
      
      .tab-content {
        padding: 1rem;
      }
    }
  `]
})
export class UserProfileComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  profileForm!: FormGroup;
  passwordForm!: FormGroup;
  currentUser: UserDto | null = null;
  isLoading = false;
  isUpdating = false;
  isChangingPassword = false;

  constructor(
    private readonly fb: FormBuilder,
    private readonly authService: AuthService,
    private readonly userService: UserService,
    private readonly snackBar: MatSnackBar
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {
    this.loadUserProfile();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForms(): void {
    this.profileForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      userName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['']
    });

    this.passwordForm = this.fb.group({
      currentPassword: ['', Validators.required],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required]
    }, { validators: this.passwordMatchValidator });
  }

  private passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');
    
    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  private loadUserProfile(): void {
    this.isLoading = true;
    
    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (user) => {
          this.currentUser = user;
          if (user) {
            this.profileForm.patchValue({
              firstName: user.firstName || '',
              lastName: user.lastName || '',
              userName: user.userName || '',
              email: user.email || '',
              phoneNumber: user.phoneNumber || ''
            });
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading user profile:', error);
          this.snackBar.open('Error loading profile', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
  }

  updateProfile(): void {
    if (this.profileForm.valid && this.currentUser) {
      this.isUpdating = true;

      const updateData = this.profileForm.value;

      this.userService.updateProfile(updateData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.snackBar.open('Profile updated successfully', 'Close', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
            this.isUpdating = false;
          },
          error: (error) => {
            console.error('Error updating profile:', error);

            // Handle specific HTTP error codes
            if (error.status === 405) {
              this.snackBar.open('Profile update is not available yet. Please contact your administrator.', 'Close', {
                duration: 5000,
                panelClass: ['error-snackbar']
              });
            } else if (error.status === 403) {
              this.snackBar.open('You do not have permission to update this profile.', 'Close', {
                duration: 5000,
                panelClass: ['error-snackbar']
              });
            } else {
              this.snackBar.open('Error updating profile. Please try again later.', 'Close', {
                duration: 5000,
                panelClass: ['error-snackbar']
              });
            }

            this.isUpdating = false;
          }
        });
    }
  }

  changePassword(): void {
    if (this.passwordForm.valid && this.currentUser) {
      this.isChangingPassword = true;
      
      const passwordData = {
        currentPassword: this.passwordForm.value.currentPassword,
        newPassword: this.passwordForm.value.newPassword,
        confirmPassword: this.passwordForm.value.confirmPassword
      };
      
      this.authService.changePassword(passwordData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.snackBar.open('Password changed successfully', 'Close', { duration: 3000 });
            this.resetPasswordForm();
            this.isChangingPassword = false;
          },
          error: (error: any) => {
            console.error('Error changing password:', error);
            this.snackBar.open('Error changing password', 'Close', { duration: 3000 });
            this.isChangingPassword = false;
          }
        });
    }
  }

  resetForm(): void {
    this.loadUserProfile();
  }

  resetPasswordForm(): void {
    this.passwordForm.reset();
  }
}
