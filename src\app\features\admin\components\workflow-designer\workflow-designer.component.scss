.workflow-designer-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  mat-card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
  }

  mat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;

      mat-icon {
        color: #1976d2;
      }
    }

    .header-actions {
      display: flex;
      gap: 1rem;
    }
  }

  .designer-placeholder {
    text-align: center;
    padding: 3rem 2rem;

    .placeholder-icon {
      margin-bottom: 1rem;

      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #1976d2;
        opacity: 0.7;
      }
    }

    h2 {
      margin: 1rem 0;
      color: #333;
      font-weight: 500;
    }

    p {
      color: #666;
      font-size: 1.1rem;
      margin-bottom: 2rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .feature-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin: 2rem 0;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;

      .feature-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #1976d2;

        mat-icon {
          color: #1976d2;
        }

        span {
          font-weight: 500;
          color: #333;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;
      flex-wrap: wrap;
    }
  }
}

@media (max-width: 768px) {
  .workflow-designer-container {
    padding: 1rem;

    mat-card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .header-actions {
        width: 100%;
        justify-content: flex-start;
      }
    }

    .designer-placeholder {
      padding: 2rem 1rem;

      .feature-list {
        grid-template-columns: 1fr;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;

        button {
          width: 100%;
          max-width: 300px;
        }
      }
    }
  }
}