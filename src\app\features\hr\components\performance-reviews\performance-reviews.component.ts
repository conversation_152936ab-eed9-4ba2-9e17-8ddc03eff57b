import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { CreateReviewDialogComponent } from '../create-review-dialog/create-review-dialog.component';
import { EditReviewDialogComponent } from '../edit-review-dialog/edit-review-dialog.component';
import { ReviewDetailsDialogComponent } from '../review-details-dialog/review-details-dialog.component';
import { MatDivider } from "@angular/material/divider";
import { 
  PerformanceReviewService, 
  PerformanceReview, 
  ReviewType, 
  ReviewStatus, 
  ReviewPriority 
} from '../../../../core/services/performance-review.service';
import { PaginationParams } from '../../../../core/models';

@Component({
  selector: 'app-performance-reviews',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatChipsModule,
    MatTabsModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatSnackBarModule,
    MatMenuModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatDivider
],
  template: `
    <div class="performance-reviews">
      <div class="header">
        <h1>
          <mat-icon>star_rate</mat-icon>
          Performance Reviews
        </h1>
        <p>Manage employee performance evaluations and reviews</p>
      </div>

      <div class="overview-cards">
        <mat-card class="metric-card due">
          <mat-card-content>
            <div class="metric">
              <mat-icon class="metric-icon">assignment_turned_in</mat-icon>
              <div class="metric-info">
                <h3>{{ getReviewsByStatus('in-progress').length + getReviewsByStatus('draft').length }}</h3>
                <p>Due This Quarter</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card completed">
          <mat-card-content>
            <div class="metric">
              <mat-icon class="metric-icon">trending_up</mat-icon>
              <div class="metric-info">
                <h3>{{ getReviewsByStatus('completed').length }}</h3>
                <p>Completed</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card overdue">
          <mat-card-content>
            <div class="metric">
              <mat-icon class="metric-icon">schedule</mat-icon>
              <div class="metric-info">
                <h3>{{ getReviewsByStatus('overdue').length }}</h3>
                <p>Overdue</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card total">
          <mat-card-content>
            <div class="metric">
              <mat-icon class="metric-icon">assessment</mat-icon>
              <div class="metric-info">
                <h3>{{ reviews.length }}</h3>
                <p>Total Reviews</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <mat-card class="reviews-management">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>list</mat-icon>
            Performance Reviews
          </mat-card-title>
          <div class="header-actions">
            <button mat-raised-button color="primary" (click)="createReview()">
              <mat-icon>add</mat-icon>
              Create Review
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <!-- Filters -->
          <div class="filters-section">
            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Filter by Status</mat-label>
              <mat-select [(value)]="selectedStatusFilter" (selectionChange)="applyFilters()">
                <mat-option value="">All Statuses</mat-option>
                <mat-option value="draft">Draft</mat-option>
                <mat-option value="in-progress">In Progress</mat-option>
                <mat-option value="completed">Completed</mat-option>
                <mat-option value="overdue">Overdue</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Filter by Type</mat-label>
              <mat-select [(value)]="selectedTypeFilter" (selectionChange)="applyFilters()">
                <mat-option value="">All Types</mat-option>
                <mat-option value="annual">Annual Review</mat-option>
                <mat-option value="quarterly">Quarterly Review</mat-option>
                <mat-option value="probationary">Probationary Review</mat-option>
                <mat-option value="project">Project Review</mat-option>
                <mat-option value="360">360-Degree Review</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Search Reviews</mat-label>
              <input matInput [(ngModel)]="searchTerm" (input)="applyFilters()" 
                     placeholder="Search by employee name or title">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
          </div>

          <!-- Loading Indicator -->
          <div *ngIf="isLoading" class="loading-container">
            <mat-spinner diameter="50"></mat-spinner>
            <p>Loading performance reviews...</p>
          </div>

          <!-- Reviews Table -->
          <div *ngIf="!isLoading" class="table-container">
            <table mat-table [dataSource]="filteredReviews" class="reviews-table">
              <!-- Employee Column -->
              <ng-container matColumnDef="employee">
                <th mat-header-cell *matHeaderCellDef>Employee</th>
                <td mat-cell *matCellDef="let review">
                  <div class="employee-info">
                    <strong>{{ review.employeeName }}</strong>
                    <small>{{ review.employeePosition }}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Review Title Column -->
              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Review Title</th>
                <td mat-cell *matCellDef="let review">
                  <div class="review-title">
                    <span>{{ review.title }}</span>
                    <mat-chip [class]="'priority-' + getPriorityClass(review.priority)" class="priority-chip">
                      {{ getPriorityDisplay(review.priority) }}
                    </mat-chip>
                  </div>
                </td>
              </ng-container>

              <!-- Type Column -->
              <ng-container matColumnDef="type">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let review">
                  <span class="review-type">{{ getReviewTypeDisplay(review.reviewType) }}</span>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let review">
                  <mat-chip [class]="'status-' + review.status">
                    {{ getStatusDisplay(review.status) }}
                  </mat-chip>
                </td>
              </ng-container>

              <!-- Progress Column -->
              <ng-container matColumnDef="progress">
                <th mat-header-cell *matHeaderCellDef>Progress</th>
                <td mat-cell *matCellDef="let review">
                  <div class="progress-container">
                    <mat-progress-bar mode="determinate" [value]="review.progress"></mat-progress-bar>
                    <span class="progress-text">{{ review.progress }}%</span>
                  </div>
                </td>
              </ng-container>

              <!-- Due Date Column -->
              <ng-container matColumnDef="dueDate">
                <th mat-header-cell *matHeaderCellDef>Due Date</th>
                <td mat-cell *matCellDef="let review">
                  <div class="due-date" [class.overdue]="isOverdue(review.dueDate)">
                    <mat-icon *ngIf="isOverdue(review.dueDate)">warning</mat-icon>
                    {{ review.dueDate | date:'MMM dd, yyyy' }}
                  </div>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let review">
                  <div class="action-buttons">
                    <button mat-icon-button [matMenuTriggerFor]="actionMenu" matTooltip="More actions">
                      <mat-icon>more_vert</mat-icon>
                    </button>
                    <mat-menu #actionMenu="matMenu">
                      <button mat-menu-item (click)="viewReview(review)">
                        <mat-icon>visibility</mat-icon>
                        View Details
                      </button>
                      <button mat-menu-item (click)="editReview(review)">
                        <mat-icon>edit</mat-icon>
                        Edit Review
                      </button>
                      <button mat-menu-item (click)="duplicateReview(review)">
                        <mat-icon>content_copy</mat-icon>
                        Duplicate
                      </button>
                      <mat-divider></mat-divider>
                      <button mat-menu-item (click)="deleteReview(review)" class="delete-action">
                        <mat-icon>delete</mat-icon>
                        Delete
                      </button>
                    </mat-menu>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="review-row"></tr>
            </table>
          </div>

          <div *ngIf="filteredReviews.length === 0" class="no-reviews">
            <mat-icon>search_off</mat-icon>
            <h3>No reviews found</h3>
            <p>Try adjusting your filters or create a new review.</p>
            <button mat-raised-button color="primary" (click)="createReview()">
              <mat-icon>add</mat-icon>
              Create First Review
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .performance-reviews {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      margin-bottom: 2rem;
      text-align: center;
    }

    .header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      margin: 0 0 0.5rem 0;
      color: #2e7d32;
      font-size: 2rem;
    }

    .header p {
      color: #666;
      font-size: 1.1rem;
      margin: 0;
    }

    .overview-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .metric-card {
      color: white;
    }

    .metric-card.due {
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    }

    .metric-card.completed {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    }

    .metric-card.overdue {
      background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    }

    .metric-card.total {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    }

    .metric {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .metric-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }

    .metric-info h3 {
      margin: 0;
      font-size: 2rem;
      font-weight: bold;
    }

    .metric-info p {
      margin: 0.25rem 0;
      font-size: 1rem;
    }

    .reviews-management {
      margin-bottom: 2rem;
    }

    .reviews-management mat-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .reviews-management mat-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .filters-section {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
    }

    .filter-field {
      min-width: 200px;
      flex: 1;
    }

    .table-container {
      overflow-x: auto;
    }

    .reviews-table {
      width: 100%;
    }

    .review-row:hover {
      background-color: #f5f5f5;
    }

    .employee-info {
      display: flex;
      flex-direction: column;
    }

    .employee-info strong {
      font-size: 1rem;
    }

    .employee-info small {
      color: #666;
      font-size: 0.8rem;
    }

    .review-title {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .priority-chip {
      font-size: 0.7rem;
      height: 20px;
      align-self: flex-start;
    }

    .priority-high {
      background-color: #ffebee;
      color: #d32f2f;
    }

    .priority-medium {
      background-color: #fff3e0;
      color: #f57c00;
    }

    .priority-low {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .priority-critical {
      background-color: #3f1a1a;
      color: #ffffff;
      font-weight: bold;
    }

    .review-type {
      color: #666;
      font-size: 0.9rem;
    }

    .status-draft {
      background-color: #f5f5f5;
      color: #666;
    }

    .status-in-progress {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .status-completed {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-overdue {
      background-color: #ffebee;
      color: #d32f2f;
    }

    .progress-container {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      min-width: 120px;
    }

    .progress-container mat-progress-bar {
      flex: 1;
    }

    .progress-text {
      font-size: 0.8rem;
      color: #666;
      min-width: 35px;
    }

    .due-date {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      color: #666;
    }

    .due-date.overdue {
      color: #d32f2f;
    }

    .due-date.overdue mat-icon {
      color: #d32f2f;
      font-size: 1.2rem;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
    }

    .delete-action {
      color: #d32f2f;
    }

    .no-reviews {
      text-align: center;
      padding: 3rem;
      color: #666;
    }

    .no-reviews mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      color: #ccc;
    }

    .no-reviews h3 {
      margin: 0 0 1rem 0;
      color: #333;
    }

    .no-reviews p {
      margin: 0 0 2rem 0;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      color: #666;
    }

    .loading-container p {
      margin-top: 1rem;
      font-size: 1.1rem;
    }

    @media (max-width: 768px) {
      .filters-section {
        flex-direction: column;
      }

      .filter-field {
        min-width: auto;
      }

      .reviews-management mat-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }

      .action-buttons {
        flex-direction: column;
      }
    }
  `]
})
export class PerformanceReviewsComponent implements OnInit {
  reviews: PerformanceReview[] = [];
  filteredReviews: PerformanceReview[] = [];
  displayedColumns: string[] = ['employee', 'title', 'type', 'status', 'progress', 'dueDate', 'actions'];
  
  selectedStatusFilter = '';
  selectedTypeFilter = '';
  searchTerm = '';
  
  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  isLoading = false;

  constructor(
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private performanceReviewService: PerformanceReviewService
  ) {}

  ngOnInit(): void {
    this.loadReviews();
  }

  loadReviews(): void {
    this.isLoading = true;
    
    const params: PaginationParams = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm || undefined,
      status: this.selectedStatusFilter ? this.getStatusEnumValue(this.selectedStatusFilter) : undefined,
      type: this.selectedTypeFilter ? this.getTypeEnumValue(this.selectedTypeFilter) : undefined,
      sortBy: 'createdDate',
      sortDirection: 'desc'
    };

    this.performanceReviewService.getPerformanceReviews(params).subscribe({
      next: (response) => {
        this.reviews = response.data;
        this.filteredReviews = response.data;
        this.totalCount = response.totalCount;
        this.isLoading = false;
        console.log('Performance reviews loaded:', response);
      },
      error: (error) => {
        console.error('Error loading performance reviews:', error);
        this.isLoading = false;
        this.snackBar.open('Error loading performance reviews. Please try again.', 'Close', { 
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        // Fallback to empty array
        this.reviews = [];
        this.filteredReviews = [];
        this.totalCount = 0;
      }
    });
  }

  applyFilters(): void {
    // Reset to first page when applying filters
    this.currentPage = 1;
    this.loadReviews();
  }

  getReviewsByStatus(status: string): PerformanceReview[] {
    const statusEnum = this.getStatusEnumValue(status);
    return this.reviews.filter(review => review.status === statusEnum);
  }

  // Helper methods for enum conversions
  getStatusEnumValue(status: string): ReviewStatus {
    switch (status) {
      case 'draft': return ReviewStatus.Draft;
      case 'in-progress': return ReviewStatus.InProgress;
      case 'completed': return ReviewStatus.Completed;
      case 'overdue': return ReviewStatus.Overdue;
      default: return ReviewStatus.Draft;
    }
  }

  getTypeEnumValue(type: string): ReviewType {
    switch (type) {
      case 'annual': return ReviewType.Annual;
      case 'quarterly': return ReviewType.Quarterly;
      case 'probationary': return ReviewType.Probationary;
      case 'project': return ReviewType.Project;
      case '360': return ReviewType.ThreeSixty;
      default: return ReviewType.Annual;
    }
  }

  getReviewTypeDisplay(type: ReviewType): string {
    return this.performanceReviewService.getReviewTypeLabel(type);
  }

  getStatusDisplay(status: ReviewStatus): string {
    return this.performanceReviewService.getReviewStatusLabel(status);
  }

  getPriorityDisplay(priority: ReviewPriority): string {
    return this.performanceReviewService.getReviewPriorityLabel(priority);
  }

  getPriorityClass(priority: ReviewPriority): string {
    switch (priority) {
      case ReviewPriority.Low: return 'low';
      case ReviewPriority.Medium: return 'medium';
      case ReviewPriority.High: return 'high';
      case ReviewPriority.Critical: return 'critical';
      default: return 'medium';
    }
  }

  isOverdue(dueDate: string): boolean {
    return new Date() > new Date(dueDate);
  }

  createReview(): void {
    const dialogRef = this.dialog.open(CreateReviewDialogComponent, {
      width: '800px',
      maxHeight: '90vh',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const createDto = {
          title: result.title,
          employeeId: result.employeeId,
          reviewType: result.reviewType,
          dueDate: result.dueDate,
          priority: result.priority || ReviewPriority.Medium,
          description: result.description,
          goals: result.goals
        };

        this.performanceReviewService.createPerformanceReview(createDto).subscribe({
          next: (createdReview) => {
            this.loadReviews(); // Reload the list
            this.snackBar.open('Performance review created successfully!', 'Close', { duration: 3000 });
          },
          error: (error) => {
            console.error('Error creating performance review:', error);
            this.snackBar.open('Error creating performance review. Please try again.', 'Close', { 
              duration: 5000,
              panelClass: ['error-snackbar']
            });
          }
        });
      }
    });
  }

  viewReview(review: PerformanceReview): void {
    const dialogRef = this.dialog.open(ReviewDetailsDialogComponent, {
      width: '900px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      data: { review }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'edit') {
        this.editReview(result.review);
      }
    });
  }

  editReview(review: PerformanceReview): void {
    const dialogRef = this.dialog.open(EditReviewDialogComponent, {
      width: '800px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      disableClose: true,
      data: { review }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadReviews(); // Reload the list to show updated data
        this.snackBar.open('Performance review updated successfully!', 'Close', { duration: 3000 });
      }
    });
  }

  duplicateReview(review: PerformanceReview): void {
    const createDto = {
      title: `${review.title} (Copy)`,
      employeeId: review.employeeId,
      reviewType: review.reviewType,
      dueDate: review.dueDate,
      priority: review.priority,
      description: review.description,
      goals: review.goals
    };

    this.performanceReviewService.createPerformanceReview(createDto).subscribe({
      next: (duplicatedReview) => {
        this.loadReviews(); // Reload the list
        this.snackBar.open('Review duplicated successfully!', 'Close', { duration: 3000 });
      },
      error: (error) => {
        console.error('Error duplicating performance review:', error);
        this.snackBar.open('Error duplicating performance review. Please try again.', 'Close', { 
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  deleteReview(review: PerformanceReview): void {
    if (confirm(`Are you sure you want to delete the review "${review.title}"?`)) {
      this.performanceReviewService.deletePerformanceReview(review.id).subscribe({
        next: () => {
          this.loadReviews(); // Reload the list
          this.snackBar.open('Review deleted successfully!', 'Close', { duration: 3000 });
        },
        error: (error) => {
          console.error('Error deleting performance review:', error);
          this.snackBar.open('Error deleting performance review. Please try again.', 'Close', { 
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }
}