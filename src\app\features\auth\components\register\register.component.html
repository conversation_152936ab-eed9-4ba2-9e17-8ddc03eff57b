<div class="register-container">
  <div class="register-card">
    <div class="register-header">
      <h1>Create Account</h1>
      <p>Join BPM Light platform</p>
    </div>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
      <div class="form-row">
        <div class="form-group">
          <label for="userName">Username *</label>
          <input
            type="text"
            id="userName"
            formControlName="userName"
            class="form-control"
            [class.is-invalid]="isFieldInvalid('userName')"
            placeholder="Enter username"
          />
          <div class="invalid-feedback" *ngIf="isFieldInvalid('userName')">
            {{ getFieldError('userName') }}
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email *</label>
          <input
            type="email"
            id="email"
            formControlName="email"
            class="form-control"
            [class.is-invalid]="isFieldInvalid('email')"
            placeholder="Enter email"
          />
          <div class="invalid-feedback" *ngIf="isFieldInvalid('email')">
            {{ getFieldError('email') }}
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="firstName">First Name</label>
          <input
            type="text"
            id="firstName"
            formControlName="firstName"
            class="form-control"
            placeholder="Enter first name"
          />
        </div>

        <div class="form-group">
          <label for="lastName">Last Name</label>
          <input
            type="text"
            id="lastName"
            formControlName="lastName"
            class="form-control"
            placeholder="Enter last name"
          />
        </div>
      </div>

      <div class="form-group">
        <label for="phoneNumber">Phone Number</label>
        <input
          type="tel"
          id="phoneNumber"
          formControlName="phoneNumber"
          class="form-control"
          placeholder="Enter phone number"
        />
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="password">Password *</label>
          <input
            type="password"
            id="password"
            formControlName="password"
            class="form-control"
            [class.is-invalid]="isFieldInvalid('password')"
            placeholder="Enter password"
          />
          <div class="invalid-feedback" *ngIf="isFieldInvalid('password')">
            {{ getFieldError('password') }}
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirm Password *</label>
          <input
            type="password"
            id="confirmPassword"
            formControlName="confirmPassword"
            class="form-control"
            [class.is-invalid]="isFieldInvalid('confirmPassword')"
            placeholder="Confirm password"
          />
          <div class="invalid-feedback" *ngIf="isFieldInvalid('confirmPassword')">
            {{ getFieldError('confirmPassword') }}
          </div>
        </div>
      </div>

      <div class="success-message" *ngIf="successMessage">
        <i class="fas fa-check-circle"></i>
        {{ successMessage }}
      </div>

      <div class="error-message" *ngIf="errorMessage">
        <i class="fas fa-exclamation-triangle"></i>
        {{ errorMessage }}
      </div>

      <button
        type="submit"
        class="btn btn-primary btn-block"
        [disabled]="isLoading"
      >
        <span *ngIf="isLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
        {{ isLoading ? 'Creating Account...' : 'Create Account' }}
      </button>
    </form>

    <div class="register-footer">
      <p>Already have an account? <a routerLink="/auth/login">Sign in</a></p>
    </div>
  </div>
</div>
