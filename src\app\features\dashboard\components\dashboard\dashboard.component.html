<div class="dashboard-container">
  <!-- Welcome Section -->
  <div class="welcome-section">
    <div class="welcome-content">
      <h1>{{ getGreeting() }}, {{ getUserDisplayName() }}!</h1>
      <p>Welcome to your BPM Light dashboard. Here's what's happening today.</p>
    </div>
    <div class="user-avatar">
      <i class="fas fa-user-circle"></i>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p>Loading dashboard data...</p>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading" class="dashboard-content">
    <!-- Stats Cards -->
    <div class="stats-section">
      <div class="row">
        <div class="col-md-3 col-sm-6">
          <app-stats-card
            title="Total Requests"
            [value]="requestSummary?.totalRequests || 0"
            icon="fas fa-file-alt"
            color="primary">
          </app-stats-card>
        </div>
        <div class="col-md-3 col-sm-6">
          <app-stats-card
            title="Pending Requests"
            [value]="requestSummary?.pendingRequests || 0"
            icon="fas fa-clock"
            color="warning">
          </app-stats-card>
        </div>
        <div class="col-md-3 col-sm-6">
          <app-stats-card
            title="Approved Requests"
            [value]="requestSummary?.approvedRequests || 0"
            icon="fas fa-check-circle"
            color="success">
          </app-stats-card>
        </div>
        <div class="col-md-3 col-sm-6">
          <app-stats-card
            title="Unread Notifications"
            [value]="notificationSummary?.unreadNotifications || 0"
            icon="fas fa-bell"
            color="info">
          </app-stats-card>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="main-content">
      <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-4 col-md-6">
          <app-quick-actions></app-quick-actions>
        </div>

        <!-- Recent Requests -->
        <div class="col-lg-8 col-md-6">
          <app-recent-requests></app-recent-requests>
        </div>
      </div>
    </div>

    <!-- Role-based Sections -->
    <div class="role-sections">
      <!-- Manager Section -->
      <div *ngIf="hasRole('Manager')" class="manager-section">
        <div class="section-card">
          <div class="section-header">
            <h3><i class="fas fa-users"></i> Manager Dashboard</h3>
          </div>
          <div class="section-content">
            <p>You have {{ requestSummary?.pendingRequests || 0 }} requests waiting for your approval.</p>
            <a routerLink="/requests/pending" class="btn btn-primary">
              <i class="fas fa-eye"></i> Review Pending Requests
            </a>
          </div>
        </div>
      </div>

      <!-- HR Section -->
      <div *ngIf="hasRole('HR')" class="hr-section">
        <div class="section-card">
          <div class="section-header">
            <h3><i class="fas fa-user-tie"></i> HR Dashboard</h3>
          </div>
          <div class="section-content">
            <p>Manage employee requests and process approved items.</p>
            <div class="action-buttons">
              <a routerLink="/requests" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> All Requests
              </a>
              <a routerLink="/reports" class="btn btn-outline-secondary">
                <i class="fas fa-chart-bar"></i> Reports
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Admin Section -->
      <div *ngIf="hasRole('Admin')" class="admin-section">
        <div class="section-card">
          <div class="section-header">
            <h3><i class="fas fa-cog"></i> Admin Dashboard</h3>
          </div>
          <div class="section-content">
            <p>Configure workflows, manage users, and monitor system performance.</p>
            <div class="action-buttons">
              <a routerLink="/admin/workflows" class="btn btn-outline-primary">
                <i class="fas fa-project-diagram"></i> Workflows
              </a>
              <a routerLink="/admin/users" class="btn btn-outline-secondary">
                <i class="fas fa-users-cog"></i> Users
              </a>
              <a routerLink="/admin/settings" class="btn btn-outline-info">
                <i class="fas fa-sliders-h"></i> Settings
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
