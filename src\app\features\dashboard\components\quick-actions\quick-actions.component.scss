.quick-actions-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .card-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;

    h3 {
      margin: 0;
      color: #333;
      font-size: 1.2rem;

      i {
        margin-right: 10px;
        color: #667eea;
      }
    }
  }

  .card-body {
    padding: 20px;

    .action-buttons {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;

      .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;

        &:hover {
          background: #667eea;
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        i {
          font-size: 24px;
          margin-bottom: 10px;
        }

        span {
          font-size: 0.9rem;
          font-weight: 500;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .quick-actions-card .card-body .action-buttons {
    grid-template-columns: 1fr;
  }
}
